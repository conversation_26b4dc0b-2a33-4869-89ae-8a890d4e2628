import json
import sys
from datetime import datetime, timedelta
import pytz
import pandas as pd
from sqlalchemy import create_engine, text
from NorenRestApiPy.NorenApi import NorenApi
import eq_cred as cr  # Assuming this has DB_USER, etc.

# ShoonyaSessionLoader class (as provided)
class ShoonyaSessionLoader:
    REST_URL = "https://api.shoonya.com/NorenWClientTP/"
    WS_URL   = "wss://api.shoonya.com/NorenWSTP/"

    def __init__(self, cred_file: str = "Cread.json", token_file: str = "session_token.txt"):
        self.cred_file  = cred_file
        self.token_file = token_file
        self._api       = None
        self._creds     = None

    @property
    def api(self) -> NorenApi:
        """Returns the ready-to-use NorenApi instance after session load."""
        return self._api

    def load(self) -> bool:
        """Load credentials and session token, initialize NorenApi."""
        try:
            with open(self.cred_file, "r") as fp:
                self._creds = json.load(fp)
            with open(self.token_file, "r") as fp:
                token = fp.read().strip()
        except Exception as e:
            print(f"[ShoonyaSessionLoader] Error loading files: {e}")
            return False

        class _Api(NorenApi):
            def __init__(self_):
                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)

        self._api = _Api()
        self._api.set_session(self._creds["user_id"], self._creds["password"], token)
        print("[ShoonyaSessionLoader] Session loaded successfully.")
        return True

# Constants
IST = pytz.timezone('Asia/Kolkata')
DB_URL = f"postgresql://{cr.DB_USER}:{cr.DB_PASS_ENCODED}@{cr.DB_HOST}:{cr.DB_PORT}/{cr.DB_NAME}"
ENGINE = create_engine(DB_URL)

def find_token(api, symbol: str, exchange: str = 'NSE') -> str:
    """Find token for given symbol using searchscrip."""
    ret = api.searchscrip(exchange=exchange, searchtext=symbol)
    if ret and ret.get('values'):
        for sym in ret['values']:
            if sym['tsym'].upper() == symbol.upper():
                print(f"Found token {sym['token']} for {sym['tsym']}")
                return sym['token']
    print(f"Token not found for {symbol}")
    return None

def classify_market_order(row: pd.Series) -> tuple[str, float]:
    """
    Classify the trade as 'buy', 'sell', or 'none' based on LTP vs best bid/ask.
    Assumes DB has columns: best_bid_price, best_ask_price, ltp, ltq
    Returns (order_type, ltq)
    """
    ltp = row['ltp']
    ltq = row['ltq']
    best_bid = row['best_bid_price']
    best_ask = row['best_ask_price']
    
    if ltq <= 0 or pd.isna(ltp) or pd.isna(best_bid) or pd.isna(best_ask):
        return 'none', 0.0
    
    tolerance = 0.01  # For floating point comparison
    if abs(ltp - best_ask) <= tolerance:
        return 'buy', ltq  # Market buy hits ask
    elif abs(ltp - best_bid) <= tolerance:
        return 'sell', ltq  # Market sell hits bid
    else:
        return 'none', 0.0

def fetch_quotes_data(token: str, start_time: datetime, end_time: datetime) -> pd.DataFrame:
    """Fetch quotes from DB for the token in the time range, convert timestamps to IST."""
    query = text("""
        SELECT 
            timestamp_added, ltt, ltp, ltq, v, best_bid_price, best_ask_price
        FROM quotes 
        WHERE token = :token 
        AND timestamp_added BETWEEN :start AND :end
        ORDER BY timestamp_added
    """)
    df = pd.read_sql(query, ENGINE, params={'token': token, 'start': start_time, 'end': end_time})
    
    if not df.empty:
        # Convert timestamp_added to IST
        df['timestamp_added'] = pd.to_datetime(df['timestamp_added']).dt.tz_convert(IST)
        # ltt is already IST string, parse if needed
        df['ltt_parsed'] = pd.to_datetime(df['ltt'], format='%Y-%m-%d %H:%M:%S IST', errors='coerce')
    
    # Calculate volume change (delta v)
    df['v_delta'] = df['v'].diff().fillna(0)
    
    return df

def analyze_market_orders(df: pd.DataFrame) -> pd.DataFrame:
    """Analyze and aggregate market orders by LTP."""
    # Classify each row
    df[['order_type', 'trade_qty']] = df.apply(classify_market_order, axis=1, result_type='expand')
    
    # Filter only market orders
    market_orders = df[df['order_type'] != 'none'].copy()
    
    if market_orders.empty:
        print("No market orders found.")
        return pd.DataFrame()
    
    # Group by LTP
    agg = market_orders.groupby('ltp').agg(
        buy_count=('order_type', lambda x: (x == 'buy').sum()),
        sell_count=('order_type', lambda x: (x == 'sell').sum()),
        buy_volume=('trade_qty', lambda x: x[x.index[df['order_type'] == 'buy']].sum() if 'buy' in x.index else 0),
        sell_volume=('trade_qty', lambda x: x[x.index[df['order_type'] == 'sell']].sum() if 'sell' in x.index else 0),
        ltt_list=('ltt', lambda x: ', '.join(x.dropna().astype(str))),
        v_delta_sum=('v_delta', 'sum')
    ).round(2)
    
    # Total orders per LTP
    agg['total_market_orders'] = agg['buy_count'] + agg['sell_count']
    
    return agg.reset_index()

def main():
    # Load API session
    loader = ShoonyaSessionLoader()
    if not loader.load():
        print("Failed to load Shoonya session.")
        sys.exit(1)
    api = loader.api
    
    # User inputs
    symbol = input("Enter symbol (e.g., TCS-EQ): ").strip().upper()
    start_str = input("Enter start time (YYYY-MM-DD HH:MM:SS) or press Enter for today 09:15:00: ") or "2025-10-07 09:15:00"
    end_str = input("Enter end time (YYYY-MM-DD HH:MM:SS) or press Enter for now: ") or datetime.now(IST).strftime("%Y-%m-%d %H:%M:%S")
    
    start_time = datetime.strptime(start_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=IST)
    end_time = datetime.strptime(end_str, '%Y-%m-%d %H:%M:%S').replace(tzinfo=IST)
    
    # Find token
    token = find_token(api, symbol)
    if not token:
        sys.exit(1)
    
    # Fetch data
    df = fetch_quotes_data(token, start_time, end_time)
    if df.empty:
        print("No data found for the given interval.")
        return
    
    # Analyze
    result = analyze_market_orders(df)
    
    if not result.empty:
        print(f"\nMarket Orders Analysis for {symbol} ({token}) from {start_time} to {end_time}:")
        print(result.to_string(index=False))
    else:
        print("No market orders detected.")

# Note: To enable classification, update your data collection script to store:
# 'best_bid_price': float(symbol_data.get('bp1', 0)),
# 'best_ask_price': float(symbol_data.get('sp1', 0)),  # Assuming 'ap' is not best ask; use sp1
# And add these columns to the quotes table.

if __name__ == "__main__":
    main()