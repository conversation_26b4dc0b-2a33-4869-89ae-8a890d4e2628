from NorenRestApiPy.NorenApi import NorenApi
import json
import requests as rq
import pandas as pd
import time
import datetime as dt
from pyotp import TOTP

def login():
    class ShoonyaApiPy(NorenApi):
        def __init__(self):
            NorenApi.__init__(self, host='https://api.shoonya.com/NorenWClientTP/',
                              websocket='wss://api.shoonya.com/NorenWSTP/')
    
    api = ShoonyaApiPy()
    with open("Cread.json", "r") as f:
        Cread = json.load(f)
    
    user = Cread["user_id"]
    pwd = Cread["password"]
    factor2 = Cread["totp"]
    vc = Cread["vendor_code"]
    app_key = Cread["app_key"]
    imei = Cread["imei"]
    
    otp = TOTP(factor2).now().zfill(6)
    
    ret = api.login(userid=user, password=pwd, twoFA=otp, vendor_code=vc, api_secret=app_key, imei=imei)
    
    if ret is None or 'susertoken' not in ret:
        print("Login failed. Response:", ret)
        return None
    
    print("Login Successful")
    
    with open('session_token.txt', 'w') as file:
        file.write(ret['susertoken'])
    
    return api

if __name__ == '__main__':
    
    login()
    