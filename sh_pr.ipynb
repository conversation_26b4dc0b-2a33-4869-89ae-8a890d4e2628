"""
Minimal, fast loader for Shoonya session with NorenApi instance.
"""

import json
from NorenRestApiPy.NorenApi import NorenApi

REST_URL = "https://api.shoonya.com/NorenWClientTP/"
WS_URL   = "wss://api.shoonya.com/NorenWSTP/"

class ShoonyaSessionLoader:
    def __init__(self, cred_file="Cread.json", token_file="session_token.txt"):
        self.cred_file, self.token_file = cred_file, token_file
        self.api = None

    def load(self):
        try:
            with open(self.cred_file) as cf, open(self.token_file) as tf:
                creds, token = json.load(cf), tf.read().strip()
            self.api = NorenApi(host=REST_URL, websocket=WS_URL)
            self.api.set_session(creds["user_id"], creds["password"], token)
            print("[ShoonyaSessionLoader] Session loaded.")
            return True
        except Exception as e:
            print(f"[ShoonyaSessionLoader] Error: {e}")
            return False

# Example usage
if __name__ == "__main__":
    loader = ShoonyaSessionLoader()
    if loader.load():
        api = loader.api
        # Use api e.g. api.get_order_book()


exch  = 'NFO'
query = 'NIFTY07OCT25C25000' # multiple criteria to narrow results 
ret = api.searchscrip(exchange=exch, searchtext=query)

if ret != None:
    symbols = ret['values']
    for symbol in symbols:
        print('{0} token is {1}'.format(symbol['tsym'], symbol['token']))

exch  = 'NSE'
query = 'BSE-EQ' # multiple criteria to narrow results 
ret = api.searchscrip(exchange=exch, searchtext=query)

if ret != None:
    symbols = ret['values']
    for symbol in symbols:
        print('{0} token is {1}'.format(symbol['tsym'], symbol['token']))

exch  = 'NSE'
token = '19585'
ret = api.get_quotes(exchange=exch, token=token)
ret

import json
from datetime import datetime, timedelta
from NorenRestApiPy.NorenApi import NorenApi

REST_URL = "https://api.shoonya.com/NorenWClientTP/"
WS_URL   = "wss://api.shoonya.com/NorenWSTP/"

def load_session(cred_file="Cread.json", token_file="session_token.txt") -> NorenApi | None:
    try:
        creds = json.load(open(cred_file))
        token = open(token_file).read().strip()
        api = NorenApi(host=REST_URL, websocket=WS_URL)
        api.set_session(creds["user_id"], creds["password"], token)
        print("[ShoonyaSession] Session loaded.")
        return api
    except Exception as e:
        print(f"[ShoonyaSession] Error: {e}")
        return None

def get_token(api: NorenApi, exch: str, query: str) -> str | None:
    ret = api.searchscrip(exchange=exch, searchtext=query)
    if ret and "values" in ret:
        for symbol in ret["values"]:
            print(f"{symbol['tsym']} token is {symbol['token']}")
            return symbol["token"]
    return None

def convert_unix_to_ist(ts: str) -> str:
    try:
        dt_utc = datetime.utcfromtimestamp(int(ts))
        dt_ist = dt_utc + timedelta(hours=5, minutes=30)
        return dt_ist.strftime("%Y-%m-%d %H:%M:%S IST")
    except:
        return ts

def get_quote(api: NorenApi, exch: str, token: str):
    ret = api.get_quotes(exchange=exch, token=token)
    if ret and "lut" in ret:
        ret["lut"] = convert_unix_to_ist(ret["lut"])
    print(json.dumps(ret, indent=2))

# ----------------------------- #
# Run the integrated workflow
# ----------------------------- #
if __name__ == "__main__":
    api = load_session()
    if api:
        exch, query = "NSE", "BSE-EQ"
        token = get_token(api, exch, query)
        if token:
            get_quote(api, exch, token)