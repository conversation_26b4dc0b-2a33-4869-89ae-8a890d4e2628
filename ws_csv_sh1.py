import signal
import sys
import json
import threading
import keyboard
import csv
from datetime import datetime, timedelta
from NorenRestApiPy.NorenApi import NorenApi

class ShoonyaApiHandler(NorenApi):
    def __init__(self, exchange: str, token: str):
        super().__init__(host='https://api.shoonya.com/NorenWClientTP/',
                         websocket='wss://api.shoonya.com/NorenWSTP/')
        self.trading_symbol = f"{exchange}|{token}"
        self.feed_opened = False
        self.running = threading.Event()
        self.running.set()

        # CSV setup
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.csv_file = open(f"tick_data_{timestamp}.csv", mode="w", newline="")
        self.csv_writer = None

    def login(self):
        with open("Cread.json", "r") as f:
            creds = json.load(f)
        user, pwd = creds["user_id"], creds["password"]
        access_token = open('session_token.txt', 'r').read().strip()
        self.set_session(user, pwd, access_token)

    def open_callback(self):
        self.feed_opened = True
        print('WebSocket Opened ... \n')

    def close_callback(self):
        self.feed_opened = False
        print("\nWebSocket closed gracefully.")

    def event_handler_feed_update(self, tick_data):
        if not self.running.is_set():
            return

        # Convert ft to IST string format
        if "ft" in tick_data:
            try:
                ts = datetime.utcfromtimestamp(int(tick_data["ft"])) + timedelta(hours=5, minutes=30)
                tick_data["ft"] = ts.strftime("%Y-%m-%d %H:%M:%S IST")
            except:
                tick_data["ft"] = "Invalid timestamp"

        # Write to CSV
        if self.csv_writer is None:
            self.csv_writer = csv.DictWriter(self.csv_file, fieldnames=tick_data.keys())
            self.csv_writer.writeheader()
        self.csv_writer.writerow(tick_data)

        sys.stdout.write("\r" + json.dumps(tick_data, indent=2))
        sys.stdout.flush()

    def start(self):
        self.start_websocket(order_update_callback=None,
                             subscribe_callback=self.event_handler_feed_update,
                             socket_open_callback=self.open_callback,
                             socket_close_callback=self.close_callback)

        while not self.feed_opened:
            pass

        self.subscribe(self.trading_symbol)

    def stop_execution(self, *args):
        print("\nUnsubscribing and closing WebSocket...")
        self.running.clear()
        if self.feed_opened:
            self.unsubscribe(self.trading_symbol)
            self.close_websocket()
        if self.csv_file:
            self.csv_file.close()
        print("Execution stopped gracefully.")

def monitor_keyboard(api_handler):
    print("\nPress **Escape** to stop the script...")
    while api_handler.running.is_set():
        if keyboard.is_pressed("esc"):
            api_handler.stop_execution()
            break

if __name__ == "__main__":
    exchange = 'NSE'
    token = '19585'

    api_handler = ShoonyaApiHandler(exchange=exchange, token=token)
    api_handler.login()

    signal.signal(signal.SIGINT, api_handler.stop_execution)
    signal.signal(signal.SIGTERM, api_handler.stop_execution)

    websocket_thread = threading.Thread(target=api_handler.start, daemon=True)
    websocket_thread.start()

    monitor_keyboard(api_handler)