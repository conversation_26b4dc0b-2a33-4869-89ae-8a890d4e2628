# eq_to_db_nifty_vix_tk_sh1.py
from __future__ import annotations
import json
import time
from typing import List, Dict
from NorenRestApiPy.NorenApi import NorenApi
import sqlalchemy as db
import requests
import sys
from datetime import datetime, time as dt_time, timedelta
import pytz
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.orm import sessionmaker
import eq_cred as cr
import msvcrt
import concurrent.futures

# --- Constants ---
MARKET_START_TIME = dt_time(8, 59, 55)
MARKET_END_TIME = dt_time(15, 30, 0)
FETCH_INTERVAL_SECONDS = 5
IST = pytz.timezone('Asia/Kolkata')
TOKENS = [
    {'token': '26000', 'tsym': 'Nifty 50', 'exchange': 'NSE'},
    {'token': '26017', 'tsym': 'INDIAVIX', 'exchange': 'NSE'},
    {'token': '11536', 'tsym': 'TCS-EQ', 'exchange': 'NSE'},
    {'token': '2885', 'tsym': 'RELIANCE-EQ', 'exchange': 'NSE'},
    {'token': '467', 'tsym': 'HDFCLIFE-EQ', 'exchange': 'NSE'},
    {'token': '3432', 'tsym': 'TATACONSUM-EQ', 'exchange': 'NSE'},
    {'token': '19585', 'tsym': 'BSE-EQ', 'exchange': 'NSE'},
    {'token': '383', 'tsym': 'BEL-EQ', 'exchange': 'NSE'},
    {'token': '3499', 'tsym': 'TATASTEEL-EQ', 'exchange': 'NSE'},
    {'token': '317', 'tsym': 'BAJFINANCE-EQ', 'exchange': 'NSE'},
]

# --- Shoonya Session Loader ---
class ShoonyaSessionLoader:
    REST_URL = "https://api.shoonya.com/NorenWClientTP/"
    WS_URL = "wss://api.shoonya.com/NorenWSTP/"

    def __init__(self, cred_file: str = "Cread.json", token_file: str = "session_token.txt"):
        self.cred_file = cred_file
        self.token_file = token_file
        self._api = None
        self._creds = None

    @property
    def api(self) -> NorenApi:
        return self._api

    def load(self) -> bool:
        try:
            # Check if files exist
            import os
            if not os.path.exists(self.cred_file):
                print(f"[ShoonyaSessionLoader] Credentials file '{self.cred_file}' not found")
                return False
            if not os.path.exists(self.token_file):
                print(f"[ShoonyaSessionLoader] Token file '{self.token_file}' not found")
                return False

            with open(self.cred_file, "r") as fp:
                self._creds = json.load(fp)
            with open(self.token_file, "r") as fp:
                token = fp.read().strip()

            # Validate token is not empty
            if not token:
                print(f"[ShoonyaSessionLoader] Token file '{self.token_file}' is empty")
                return False

        except Exception as e:
            print(f"[ShoonyaSessionLoader] Error loading files: {e}")
            return False

        class _Api(NorenApi):
            def __init__(self_):
                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)

        self._api = _Api()
        self._api.set_session(self._creds["user_id"], self._creds["password"], token)
        print("[ShoonyaSessionLoader] Session loaded successfully.")
        return True

# --- Quote Fetcher ---
def fetch_quotes(api: NorenApi, tokens: List[Dict[str, str]]) -> Dict[str, dict]:
    """
    Fetch quotes concurrently with rate limiting (max 9 parallel to respect 10/sec limit).
    :param api: NorenApi instance
    :param tokens: List of dicts with token, tsym, and exchange
    :return: Dictionary of token -> quote data
    """
    quotes = {}

    def fetch_single(token_info: Dict[str, str]):
        token = token_info['token']
        exchange = token_info['exchange']
        tsym = token_info['tsym']
        try:
            quote = api.get_quotes(exchange=exchange, token=token)
            if quote.get('stat') == 'Ok':
                return token, quote, tsym
            else:
                print(f"[ERROR] {tsym} ({token}) failed: {quote.get('emsg', 'Unknown error')}")
                return token, None, tsym
        except Exception as e:
            print(f"[ERROR] {tsym} ({token}) failed: {e}")
            return token, None, tsym

    max_workers = min(9, len(tokens))  # Cap at 9 to stay under 10 req/sec
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_info = {executor.submit(fetch_single, token_info): token_info for token_info in tokens}
        for future in concurrent.futures.as_completed(future_to_info):
            token, quote, tsym = future.result()
            if quote:
                quotes[token] = quote
                print(f"[INFO] {tsym} ({token}) fetched.")
    return quotes

# --- Network Check ---
def is_network_alive():
    try:
        requests.get("https://google.com", timeout=3)
        return True
    except:
        return False

# --- Database Setup ---
try:
    # Validate eq_cred module
    if not hasattr(cr, 'DB_USER') or not hasattr(cr, 'DB_PASS_ENCODED'):
        raise AttributeError("Missing required database credentials in eq_cred module")

    db_url = f"postgresql://{cr.DB_USER}:{cr.DB_PASS_ENCODED}@{cr.DB_HOST}:{cr.DB_PORT}/{cr.DB_NAME}"
    engine = db.create_engine(
        db_url,
        connect_args={'connect_timeout': 5},
        pool_size=5,
        max_overflow=10,
        pool_pre_ping=True
    )
    Session = sessionmaker(bind=engine)
    session = Session()
    print("✅ Database connection successful.")
except Exception as e:
    print(f"❌ Database connection failed: {e}")
    sys.exit(1)

# --- Create/Check Quotes Table ---
def setup_quotes_table():
    try:
        metadata = db.MetaData()
        quotes_table = db.Table(
            'quotes', metadata,
            db.Column('token', db.String(50)),
            db.Column('timestamp_added', db.DateTime(timezone=True)),
            db.Column('totalbuyqty', db.Numeric),
            db.Column('totalsellqty', db.Numeric),
            db.Column('o', db.Numeric),
            db.Column('h', db.Numeric),
            db.Column('l', db.Numeric),
            db.Column('c', db.Numeric),
            db.Column('ltq', db.Numeric),
            db.Column('ltt', db.Text),
            db.Column('ltp', db.Numeric),
            db.Column('v', db.Numeric),
            db.Column('atp', db.Numeric),
            db.Column('top5_total_bid_qty', db.Numeric),
            db.Column('top5_total_ask_qty', db.Numeric),
            extend_existing=True
        )
        metadata.create_all(engine)
        
        # Ensure no NULLs in key columns before handling PK (clean up if necessary)
        with engine.connect() as conn:
            null_count = conn.execute(db.text("""
                SELECT COUNT(*) FROM quotes 
                WHERE token IS NULL OR timestamp_added IS NULL
            """)).scalar()
            if null_count > 0:
                print(f"⚠️ Found {null_count} rows with NULL in key columns. Deleting them...")
                conn.execute(db.text("DELETE FROM quotes WHERE token IS NULL OR timestamp_added IS NULL"))
                conn.commit()
                print("✅ Cleaned up NULL rows.")
        
        # Now check for any existing primary key
        with engine.connect() as conn:
            existing_pk = conn.execute(db.text("""
                SELECT tc.constraint_name
                FROM information_schema.table_constraints tc
                WHERE tc.table_name = 'quotes'
                AND tc.constraint_type = 'PRIMARY KEY'
            """)).fetchone()
            
            if existing_pk:
                # Check if the existing PK is on the correct columns
                correct_pk = conn.execute(db.text("""
                    SELECT tc.constraint_name
                    FROM information_schema.table_constraints tc
                    JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.constraint_schema = kcu.constraint_schema
                    WHERE tc.table_name = 'quotes'
                    AND tc.constraint_type = 'PRIMARY KEY'
                    AND kcu.column_name IN ('token', 'timestamp_added')
                    GROUP BY tc.constraint_name
                    HAVING COUNT(DISTINCT kcu.column_name) = 2
                """)).fetchone()
                
                if not correct_pk:
                    print(f"⚠️ Existing PK '{existing_pk[0]}' is incorrect. Dropping it...")
                    conn.execute(db.text(f"ALTER TABLE quotes DROP CONSTRAINT {existing_pk[0]}"))
                    conn.commit()
                    print("⚠️ Adding correct primary key constraint to quotes table on (token, timestamp_added)...")
                    conn.execute(db.text("ALTER TABLE quotes ADD CONSTRAINT quotes_pk PRIMARY KEY (token, timestamp_added)"))
                    conn.commit()
                    print("✅ Primary key constraint updated successfully.")
                else:
                    print(f"✅ Primary key constraint '{correct_pk[0]}' already exists on correct columns.")
            else:
                print("⚠️ No primary key found. Adding primary key constraint to quotes table on (token, timestamp_added)...")
                conn.execute(db.text("ALTER TABLE quotes ADD CONSTRAINT quotes_pk PRIMARY KEY (token, timestamp_added)"))
                conn.commit()
                print("✅ Primary key constraint added successfully.")
        
        # Create index if not exists
        with engine.connect() as conn:
            conn.execute(db.text("CREATE INDEX IF NOT EXISTS idx_quotes_token_timestamp ON quotes (token, timestamp_added)"))
            conn.commit()
        
        print("✅ Quotes table setup complete.")
    except Exception as e:
        print(f"❌ Failed to setup quotes table: {e}")
        sys.exit(1)

setup_quotes_table()

# --- Shoonya API Setup ---
try:
    loader = ShoonyaSessionLoader()
    if not loader.load():
        print("❌ Shoonya API initialization failed.")
        sys.exit(1)
    api = loader.api
    print("✅ Shoonya API initialized successfully.")
except Exception as e:
    print(f"❌ Shoonya API initialization failed: {e}")
    sys.exit(1)

# --- Helper Functions ---
def is_market_hours(now: datetime = None):
    if now is None:
        now = datetime.now(IST)
    return now.weekday() < 5 and MARKET_START_TIME <= now.time() < MARKET_END_TIME

def get_next_market_open(now: datetime) -> datetime:
    """Calculate the next market open time."""
    now = now.astimezone(IST)

    if now.weekday() >= 5:  # Weekend: Sat (5) or Sun (6)
        # Calculate days to next Monday
        if now.weekday() == 5:  # Saturday
            days_to_monday = 2
        else:  # Sunday (weekday == 6)
            days_to_monday = 1

        next_monday = now + timedelta(days=days_to_monday)
        return IST.localize(datetime.combine(
            next_monday.date(),
            MARKET_START_TIME
        ))
    else:  # Weekday
        today_open = IST.localize(datetime.combine(now.date(), MARKET_START_TIME))

        if now.time() < MARKET_START_TIME:
            return today_open
        elif now.time() >= MARKET_END_TIME:
            # After close, next open is tomorrow or Monday if Friday
            tomorrow = now + timedelta(days=1)
            if tomorrow.weekday() == 5:  # Tomorrow is Saturday, skip to Monday
                monday = tomorrow + timedelta(days=2)
                return IST.localize(datetime.combine(monday.date(), MARKET_START_TIME))
            else:
                return IST.localize(datetime.combine(tomorrow.date(), MARKET_START_TIME))
        else:
            # During hours, return now (but shouldn't be called if in hours)
            return now

def process_and_store_data(tokens: List[Dict[str, str]], timestamp_now: datetime):
    if not is_network_alive():
        print("❌ Network unreachable. Skipping this cycle.")
        return

    try:
        quotes = fetch_quotes(api, tokens)
        quotes_table = db.Table('quotes', db.MetaData(), autoload_with=engine)
        records_to_insert = []

        for token_info in tokens:
            token = token_info['token']
            tsym = token_info['tsym']
            symbol_data = quotes.get(token, {})
            if not symbol_data or symbol_data.get('stat') != 'Ok':
                print(f"❌ No valid data for token {token} ({tsym})")
                continue

            # Calculate top 5 bid/ask quantities
            top5_bid_qty = sum(float(symbol_data.get(f'bq{i}', 0)) for i in range(1, 6))
            top5_ask_qty = sum(float(symbol_data.get(f'sq{i}', 0)) for i in range(1, 6))

            # Process ltt (e.g., '15:14:13' to 'YYYY-MM-DD HH:MM:SS IST')
            ltt = symbol_data.get('ltt', '')
            ltt_str = ''
            if ltt:
                try:
                    ltt_time = datetime.strptime(ltt, '%H:%M:%S').time()
                    ltt_full = IST.localize(datetime.combine(timestamp_now.date(), ltt_time))
                    ltt_str = ltt_full.strftime('%Y-%m-%d %H:%M:%S IST')
                except (ValueError, TypeError) as e:
                    print(f"⚠️ Error parsing ltt '{ltt}': {e}")
                    ltt_str = ''

            # Map fields to quotes table
            record = {
                'token': token,
                'timestamp_added': timestamp_now,
                'totalbuyqty': float(symbol_data.get('tbq', 0)),
                'totalsellqty': float(symbol_data.get('tsq', 0)),
                'o': float(symbol_data.get('o', 0)),
                'h': float(symbol_data.get('h', 0)),
                'l': float(symbol_data.get('l', 0)),
                'c': float(symbol_data.get('c', 0)),  # prev.close
                'ltq': float(symbol_data.get('ltq', 0)),
                'ltt': ltt_str,
                'ltp': float(symbol_data.get('lp', 0)),
                'v': float(symbol_data.get('v', 0)),
                'atp': float(symbol_data.get('ap', 0)),
                'top5_total_bid_qty': top5_bid_qty,
                'top5_total_ask_qty': top5_ask_qty
            }
            records_to_insert.append(record)

        if records_to_insert:
            insert_stmt = insert(quotes_table).values(records_to_insert)
            on_conflict_stmt = insert_stmt.on_conflict_do_nothing(index_elements=['token', 'timestamp_added'])
            session.execute(on_conflict_stmt)
            session.commit()
            print(f"✅ Stored {len(records_to_insert)} records at {timestamp_now.strftime('%H:%M:%S')}")
        else:
            print("🤷 No records to insert.")

    except Exception as e:
        print(f"❌ Error during data processing/storage: {e}")
        session.rollback()

def wait_for_next_interval(interval: int):
    now = datetime.now(IST)
    wait_seconds = (interval - (now.second % interval)) % interval
    if wait_seconds == 0 and now.microsecond > 100000:
        wait_seconds = interval
    print(f"🕒 Waiting {wait_seconds} seconds to align with interval...")
    time.sleep(wait_seconds)

def check_stop_key():
    """Cross-platform check for stop key (Esc or Ctrl+C)."""
    try:
        import msvcrt
        if msvcrt.kbhit():
            key = msvcrt.getch()
            if key in [b'\x1b', b'\x03']:
                return True
    except ImportError:
        pass  # Non-Windows: ignore
    except:
        pass  # Handle any other errors
    return False

def sleep_until_interruptible(target_time: datetime, check_interval: int = 60):
    """Sleep until target_time, checking for stop key every check_interval seconds."""
    now = datetime.now(IST)
    sleep_duration = (target_time - now).total_seconds()
    if sleep_duration <= 0:
        return
    end_time = time.monotonic() + sleep_duration
    while time.monotonic() < end_time:
        remaining = end_time - time.monotonic()
        sleep_amount = min(remaining, check_interval)
        time.sleep(sleep_amount)
        if check_stop_key():
            raise KeyboardInterrupt

# --- Main Loop ---
def main():
    print("--- Market Data Collector (Shoonya API) ---")

    if not is_market_hours():
        print("\n⚠️ Market is currently closed.")
        try:
            # Try Windows-specific input with timeout
            import msvcrt
            start_time = time.time()
            user_input = None
            print("Run anyway and wait for next market open? (y/n): ", end='', flush=True)
            while time.time() - start_time < 10:
                if msvcrt.kbhit():
                    char = msvcrt.getch().decode('utf-8').lower()
                    if char in ['y', 'n']:
                        user_input = char
                        print(user_input)
                        break
                time.sleep(0.1)
            if user_input != 'y':
                print("\nExiting.")
                sys.exit(0)
        except ImportError:
            # Non-Windows fallback
            user_input = input("Run anyway and wait for next market open? (y/n): ").lower()
            if user_input != 'y':
                print("Exiting.")
                sys.exit(0)
        except Exception as e:
            print(f"Input error: {e}")
            user_input = input("Run anyway and wait for next market open? (y/n): ").lower()
            if user_input != 'y':
                print("Exiting.")
                sys.exit(0)

    print("\n🚀 Starting data collection loop. Press Esc or Ctrl+C to stop.")

    try:
        while True:
            now = datetime.now(IST)
            if not is_market_hours(now):
                next_open = get_next_market_open(now)
                sleep_duration = (next_open - now).total_seconds() / 3600
                print(f"💤 Market closed. Sleeping for {sleep_duration:.1f} hours until {next_open.strftime('%Y-%m-%d %H:%M:%S IST')}.")
                sleep_until_interruptible(next_open)
                continue

            # Align to next interval before processing
            wait_for_next_interval(FETCH_INTERVAL_SECONDS)

            # Now process with aligned timestamp
            target_timestamp = datetime.now(IST).replace(microsecond=0)
            process_and_store_data(TOKENS, target_timestamp)

            # Check for stop after processing
            if check_stop_key():
                print("\n🛑 Stop signal received. Exiting.")
                break

            # After processing, check if still in hours, if not, loop will handle next open

    except KeyboardInterrupt:
        print("\n🛑 Keyboard interrupt detected. Shutting down gracefully.")
    finally:
        session.close()
        print("👋 Database session closed. Goodbye.")

if __name__ == "__main__":
    main()