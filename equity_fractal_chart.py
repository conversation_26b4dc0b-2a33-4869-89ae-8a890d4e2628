# m1.py
# this code fetches data from database and plots a chart of equities
import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)

import dash
from dash import dcc, html, callback_context
from dash.dependencies import Input, Output, State
import plotly.graph_objs as go
import pandas as pd
import numpy as np
from sqlalchemy import create_engine
from io import String<PERSON>
from datetime import datetime
import eq_cred as cr
from config import IST

# --- Constants ---
VIX_SYMBOL = "NSE:INDIAVIX-INDEX"
NIFTY_SYMBOL = "NSE:NIFTY50-INDEX"

# SYMBOL_MAP for token to symbol_name mapping (expanded with new symbols)
SYMBOL_MAP = {
    '26000': 'NSE:NIFTY50-INDEX',
    '26017': 'NSE:INDIAVIX-INDEX',
    '11536': 'NSE:TCS-EQ',
    '383': 'NSE:BEL-EQ',
    '2885': 'NSE:RELIANCE-EQ',
    '3499': 'NSE:TATASTEEL-EQ', # Added
    '317': 'NSE:BAJFINANCE-EQ', # Added
    '467': 'NSE:HDFCLIFE-EQ', # Added
    '3432': 'NSE:TATACONSUM-EQ', # Added
    '19585': 'NSE:BSE-EQ', # Added
}

# Equity symbols for dropdown (excluding indices)
EQUITY_SYMBOLS = [
    'NSE:TCS-EQ',
    'NSE:BEL-EQ',
    'NSE:TATASTEEL-EQ',
    'NSE:HDFCLIFE-EQ',
    'NSE:BAJFINANCE-EQ',
    'NSE:TATACONSUM-EQ',
    'NSE:RELIANCE-EQ',
    'NSE:BSE-EQ',
]
EQUITY_SYMBOLS.sort()
# --- Database Connection ---
db_url = f"postgresql://{cr.DB_USER}:{cr.DB_PASS_ENCODED}@{cr.DB_HOST}:{cr.DB_PORT}/{cr.DB_NAME}"
engine = create_engine(db_url)

# --- Dash App ---
app = dash.Dash(__name__, update_title='Updating...')
app.title = "Fractal Analysis Dashboard"

# --- Layout ---
app.layout = html.Div(style={'backgroundColor': '#111111', 'color': '#FFFFFF'}, children=[
    html.H2("📊 Fractal Analysis Chart", style={'textAlign': 'center'}),
    html.Div([
        html.Label("Select Date:", style={'fontWeight': 'bold'}),
        dcc.DatePickerSingle(
            id='date-picker',
            date=datetime.now(IST).date(),
            display_format='YYYY-MM-DD',
            style={'marginRight': '20px'}
        ),
        html.Label("Select Primary Symbol:", style={'fontWeight': 'bold', 'marginLeft': '20px'}),
        dcc.Dropdown(
            id='primary-symbol-selector',
            options=[{'label': sym.split(':')[1], 'value': sym} for sym in EQUITY_SYMBOLS],
            value='NSE:TCS-EQ',
            clearable=False,
            style={'width': '200px', 'color': '#000'}
        ),
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '10px'}),
    html.Div([
        html.Label("Resampling:", style={'fontWeight': 'bold', 'marginLeft': '20px'}),
        dcc.Dropdown(
            id='resample-selector',
            options=[
                {'label': 'Raw', 'value': 'RAW'},
                {'label': '30s', 'value': '30s'},
                {'label': '5min', 'value': '5min'},
                {'label': '15min', 'value': '15min'},
                {'label': '75min', 'value': '75min'},
                {'label': '1D', 'value': '1D'},
            ],
            value='RAW',
            clearable=False,
            style={'width': '150px', 'color': '#000'}
        ),
        html.Label("View Mode:", style={'fontWeight': 'bold', 'marginLeft': '20px'}),
        dcc.RadioItems(
            id='view-mode-selector',
            options=[
                {'label': 'Absolute', 'value': 'ABSOLUTE'},
                {'label': 'Normalized', 'value': 'NORMALIZED'},
            ],
            value='ABSOLUTE',
            inline=True,
        ),
        dcc.Checklist(
            id='toggle-axes',
            options=[{'label': 'Show/Hide All Axes', 'value': 'SHOW'}],
            value=['SHOW'],
            inline=True,
            style={'paddingTop': '10px'}
        )
    ], style={'display': 'flex', 'gap': '20px', 'justifyContent': 'center', 'marginBottom': '20px'}),
    dcc.Store(id='data-store'),  # To store the main dataframe
    dcc.Graph(id='analysis-chart', config={'displayModeBar': True, 'scrollZoom': True}),
    dcc.Interval(id='interval-component', interval=5*60*1000, n_intervals=0) # Update every 5 minutes
])

def ensure_tz(df, col='timestamp', tz=IST):
    """Ensure the timestamp column is timezone-aware."""
    df[col] = pd.to_datetime(df[col])
    if df[col].dt.tz is None:
        df[col] = df[col].dt.tz_localize(tz)
    else:
        df[col] = df[col].dt.tz_convert(tz)
    return df

# --- Data Fetch (adapted for quotes table) ---
def fetch_data(start_time=None, end_time=None):
    """Fetches data for a specific time range from quotes table."""
    base_query = """
        SELECT 
            timestamp_added as timestamp,
            token,
            ltp as ltp, h as high, l as low, atp as atp, o as open, c as close,
            totalbuyqty as total_buy_qty, totalsellqty as total_sell_qty, ltq as ltq,
            top5_total_bid_qty as top5_total_bid_qty, top5_total_ask_qty as top5_total_ask_qty,
            v as volume
        FROM quotes
    """
    params = {}

    if start_time and end_time:
        base_query += " WHERE timestamp_added BETWEEN %(start)s AND %(end)s"
        params = {'start': start_time, 'end': end_time}

    query = base_query + """
        ORDER BY timestamp_added;
    """
    df = pd.read_sql(query, engine, params=params)
    if not df.empty:
        df = ensure_tz(df, 'timestamp', IST)
        # Map token to symbol_name
        df['symbol_name'] = df['token'].map(SYMBOL_MAP)
        # Drop unmapped rows
        df = df.dropna(subset=['symbol_name'])
        if not df.empty:
            # Fill NaNs in numeric columns per symbol
            numeric_cols = ['ltp', 'high', 'low', 'atp', 'open', 'close', 'total_buy_qty', 'total_sell_qty', 'ltq', 'top5_total_bid_qty', 'top5_total_ask_qty', 'volume']
            for col in numeric_cols:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            df = df.sort_values('timestamp')
            # ffill/bfill per symbol
            for symbol in df['symbol_name'].unique():
                mask = df['symbol_name'] == symbol
                df.loc[mask, numeric_cols] = df.loc[mask, numeric_cols].ffill().bfill()
    return df

def safe_resample(df, rule, agg_rules):
    if df.empty:
        return pd.DataFrame()
    # Preserve symbol_name
    symbol = df['symbol_name'].iloc[0] if 'symbol_name' in df.columns else ''
    df.set_index('timestamp', inplace=True)
    resampled = df.resample(rule).agg(agg_rules)
    resampled['symbol_name'] = symbol
    return resampled.reset_index()

# --- Callback to dynamically set refresh interval ---
@app.callback(
    Output('interval-component', 'interval'),
    Input('resample-selector', 'value')
)
def set_refresh_interval(resample_value):
    """Adjusts the refresh interval based on the selected timeframe."""
    if resample_value == 'RAW' or resample_value == '30s':
        return 30 * 1000  # 30 seconds
    elif resample_value == '5min':
        return 5 * 60 * 1000  # 5 minutes
    elif resample_value == '15min':
        return 15 * 60 * 1000 # 15 minutes
    elif resample_value == '75min':
        return 75 * 60 * 1000 # 75 minutes
    elif resample_value == '1D':
        return 24 * 60 * 60 * 1000 # 24 hours (effectively disabled for the day)
    return 5 * 60 * 1000 # Default to 5 minutes

# --- Chart Callback ---
@app.callback( # This callback now manages both data fetching and chart rendering
    [Output('analysis-chart', 'figure'),
     Output('data-store', 'data')],
    [Input('interval-component', 'n_intervals'),
     Input('date-picker', 'date'),
     Input('resample-selector', 'value'),
     Input('toggle-axes', 'value'),
     Input('view-mode-selector', 'value'),
     Input('primary-symbol-selector', 'value')],
    [State('data-store', 'data')]
)
def update_chart(n, selected_date_str, selected_resample, axes_visibility, view_mode, selected_primary, stored_data):
    if selected_primary is None:
        return go.Figure().update_layout(title_text="No symbols available for the selected date.", template='plotly_dark'), dash.no_update

    ctx = callback_context
    triggered_id = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else 'interval-component'

    now_ist = datetime.now(IST)
    selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()
    is_today = selected_date == now_ist.date()
    
    # Define which timeframes should load all historical data
    historical_timeframes = ['5min', '15min', '75min', '1D']

    # Optimization: Disable interval updates when viewing a past date
    if triggered_id == 'interval-component' and not is_today:
        return dash.no_update, dash.no_update
    
    # --- Data Loading Logic ---
    # If a historical timeframe is selected, ALWAYS fetch all data, ignoring date/cache.
    if selected_resample in historical_timeframes:
        df = fetch_data() # Fetch all data
    # Full reload for a new day, symbol, or on first load (for intraday views).
    elif triggered_id in ['date-picker', 'primary-symbol-selector'] or stored_data is None:
        start_time = IST.localize(datetime.combine(selected_date, datetime.min.time()))
        end_time = now_ist if is_today else IST.localize(datetime.combine(selected_date, datetime.max.time()))
        df = fetch_data(start_time, end_time)
    # Incremental update or other changes (view mode, etc.)
    else:
        df = pd.read_json(StringIO(stored_data), orient='split')
        df['timestamp'] = pd.to_datetime(df['timestamp']).dt.tz_convert(IST)
        
        # Handle live updates for today's data (only if not in historical view)
        if triggered_id == 'interval-component' and is_today and not df.empty:
            last_timestamp = df['timestamp'].max()
            new_data = fetch_data(last_timestamp, now_ist)
            if not new_data.empty:
                # Use concat and drop duplicates to merge new data
                df = pd.concat([df, new_data]).drop_duplicates(subset=['symbol_name', 'timestamp'], keep='last')

    if df.empty:
        return go.Figure().update_layout(title_text=f"No data available for {selected_date_str}.", template='plotly_dark'), dash.no_update

    # Store the updated dataframe (as JSON)
    stored_df_json = df.to_json(orient='split', date_format='iso')

    # --- Charting logic (mostly unchanged) ---
    if df.empty:
        return go.Figure().update_layout(title_text=f"No data available for {selected_date_str}.", template='plotly_dark'), stored_df_json

    # Show selected primary, with NIFTY and VIX always
    symbol_df = df[df['symbol_name'] == selected_primary]
    vix_df = df[df['symbol_name'] == VIX_SYMBOL]
    nifty_df = df[df['symbol_name'] == NIFTY_SYMBOL]

    # If the primary symbol's dataframe is empty, show a specific message
    # Clean data by removing rows where ltp is NaN to avoid gaps
    symbol_df = symbol_df.dropna(subset=['ltp'])
    vix_df = vix_df.dropna(subset=['ltp'])
    nifty_df = nifty_df.dropna(subset=['ltp'])

    if symbol_df.empty:
        return go.Figure().update_layout(
            title_text=f"No data for {selected_primary} on {selected_date_str}.",
            template='plotly_dark'
        ), stored_df_json

    # If the primary symbol has data, but Nifty/VIX are missing, show a warning but still plot the primary.
    if nifty_df.empty or vix_df.empty:
        print(f"Warning: Missing Nifty or VIX data for {selected_date_str}, but primary symbol data exists.")


    if selected_resample != 'RAW':
        agg = {
            'ltp': 'last', 'atp': 'last', 'ltq': 'sum',
            'total_buy_qty': 'sum', 'total_sell_qty': 'sum',
            'top5_total_bid_qty': 'sum', 'top5_total_ask_qty': 'sum'
        }
        symbol_df = safe_resample(symbol_df, selected_resample, agg)
        vix_df = safe_resample(vix_df, selected_resample, {'ltp': 'last'})
        nifty_df = safe_resample(nifty_df, selected_resample, {'ltp': 'last'})

        # Clean again after resampling to remove any NaN rows created by the process
        symbol_df = symbol_df.dropna(subset=['ltp'])
        vix_df = vix_df.dropna(subset=['ltp'])
        nifty_df = nifty_df.dropna(subset=['ltp'])

    fig = go.Figure()

    def normalize_series(series):
        """Scales a series to a 0-1 range."""
        min_val = series.min()
        max_val = series.max()
        if min_val == max_val:
            return pd.Series(0.5, index=series.index) # Return a flat line at 0.5 if all values are the same
        return (series - min_val) / (max_val - min_val)

    def add_trace(df, col, name, yaxis, color, dash='solid', width=1.5, view_mode='ABSOLUTE'):
        if not df.empty and col in df.columns and df[col].notnull().any():
            y_data = df[col]
            target_yaxis = yaxis
            if view_mode == 'NORMALIZED':
                y_data = normalize_series(df[col])
                target_yaxis = 'y1' # Force all normalized traces to the primary y-axis

            fig.add_trace(go.Scatter(
                x=df['timestamp'], y=y_data, mode='lines', name=name, yaxis=target_yaxis,
                line=dict(color=color, dash=dash, width=width)
            ))

    add_trace(symbol_df, 'ltp', 'LTP', 'y1', 'lime', width=2.5, view_mode=view_mode)
    add_trace(symbol_df, 'atp', 'ATP', 'y1', 'orange', dash='dash', view_mode=view_mode)
    add_trace(vix_df, 'ltp', 'India VIX', 'y2', 'gold', view_mode=view_mode)
    add_trace(nifty_df, 'ltp', 'Nifty 50', 'y8', 'deepskyblue', view_mode=view_mode)
    add_trace(symbol_df, 'total_buy_qty', 'Total Buy Qty', 'y3', 'deepskyblue', view_mode=view_mode)
    add_trace(symbol_df, 'top5_total_bid_qty', 'Top 5 Buy Qty', 'y6', 'mediumspringgreen', view_mode=view_mode)
    add_trace(symbol_df, 'total_sell_qty', 'Total Sell Qty', 'y4', 'tomato', view_mode=view_mode)
    add_trace(symbol_df, 'top5_total_ask_qty', 'Top 5 Sell Qty', 'y7', 'violet', view_mode=view_mode)
    add_trace(symbol_df, 'ltq', 'LTQ', 'y5', 'cyan', view_mode=view_mode)

    fig.update_layout(
        title=f"{selected_primary.split(':')[1]} with Nifty & VIX - {selected_resample} View",
        template='plotly_dark',
        height=750,
        xaxis_title='Time (IST)',
        xaxis_rangeslider_visible=False,
        legend=dict(orientation='h', yanchor='bottom', y=1.02, xanchor='right', x=1),
        uirevision=selected_resample,
        margin=dict(l=80, r=80, t=100, b=50),
    )

    # For historical views, switch to a category axis to remove time gaps
    if selected_resample in historical_timeframes:
        fig.update_xaxes(type='category')

    if view_mode == 'NORMALIZED':
        # In Normalized view, use a single y-axis and hide all others
        fig.update_layout(
            yaxis=dict(title='Normalized Value', showgrid=False, range=[0, 1], visible=True),
            yaxis2=dict(visible=False),
            yaxis3=dict(visible=False),
            yaxis4=dict(visible=False),
            yaxis5=dict(visible=False),
            yaxis6=dict(visible=False),
            yaxis7=dict(visible=False),
            yaxis8=dict(visible=False),
        )
    else: # Absolute View
        show_axes = 'SHOW' in axes_visibility
        axis_config = {
            'y1': {'title': 'Price', 'side': 'left', 'color': 'lime', 'position': 0.0},
            'y2': {'title': 'India VIX', 'side': 'right', 'color': 'gold', 'position': 1.0, 'overlaying': 'y'},
            'y3': {'title': 'Total Buy', 'side': 'left', 'color': 'deepskyblue', 'position': 0.06, 'overlaying': 'y', 'type': 'log'},
            'y4': {'title': 'Total Sell', 'side': 'right', 'color': 'tomato', 'position': 0.94, 'overlaying': 'y', 'type': 'log'},
            'y5': {'title': 'LTQ', 'side': 'left', 'color': 'cyan', 'position': 0.12, 'overlaying': 'y'},
            'y6': {'title': 'Top 5 Buy', 'side': 'left', 'color': 'mediumspringgreen', 'position': 0.18, 'overlaying': 'y', 'type': 'log'},
            'y7': {'title': 'Top 5 Sell', 'side': 'right', 'color': 'violet', 'position': 0.88, 'overlaying': 'y', 'type': 'log'},
            'y8': {'title': 'Nifty 50', 'side': 'right', 'color': 'deepskyblue', 'position': 0.82, 'overlaying': 'y'},
        }

        for axis_id, cfg in axis_config.items():
            layout_key = 'yaxis' if axis_id == 'y1' else f'yaxis{axis_id[1:]}'
            fig.update_layout(**{layout_key: dict(
                title=dict(text=cfg['title'], font=dict(color=cfg['color'])), side=cfg['side'], position=cfg['position'],
                overlaying=cfg.get('overlaying'), type=cfg.get('type', 'linear'),
                showgrid=False, visible=show_axes,
                anchor='x' if cfg.get('overlaying') is None else 'free',
            )})

    return fig, stored_df_json

# --- Main ---
if __name__ == '__main__':
    app.run_server(debug=True, port=8051)