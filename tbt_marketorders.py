import signal
import sys
import json
import threading
import keyboard
import time
from datetime import datetime, timedelta
from collections import defaultdict, deque
from NorenRestApiPy.NorenApi import NorenApi

# === Real-time Market Order Inference ===
class MarketOrderAnalyzer:
    def __init__(self, interval_sec=1):
        self.interval = interval_sec
        self.last_volume = None
        self.last_tick = None
        self.buffer = deque()
        self.stats = defaultdict(lambda: {'buy_qty': 0, 'sell_qty': 0, 'buy_count': 0, 'sell_count': 0})
        self.lock = threading.Lock()

    def process_tick(self, tick):
        try:
            ts = datetime.utcfromtimestamp(int(tick["ft"])) + timedelta(hours=5, minutes=30)
            tick["ft"] = ts
            tick["lp"] = float(tick["lp"])
            tick["v"] = float(tick["v"])
            tick["bp1"] = float(tick.get("bp1", 0))
            tick["sp1"] = float(tick.get("sp1", 0))
        except Exception as e:
            return

        with self.lock:
            if self.last_volume is None:
                self.last_volume = tick["v"]
                self.last_tick = tick
                return

            vol_diff = tick["v"] - self.last_volume
            if vol_diff <= 0:
                return

            is_buy = tick["lp"] == tick["sp1"]
            is_sell = tick["lp"] == tick["bp1"]

            interval_key = tick["ft"].replace(microsecond=0)

            if is_buy:
                self.stats[interval_key]['buy_qty'] += vol_diff
                self.stats[interval_key]['buy_count'] += 1
            elif is_sell:
                self.stats[interval_key]['sell_qty'] += vol_diff
                self.stats[interval_key]['sell_count'] += 1

            self.last_volume = tick["v"]
            self.last_tick = tick

    def print_and_clear_stats(self):
        while True:
            time.sleep(self.interval)
            now = datetime.now().replace(microsecond=0)
            with self.lock:
                keys_to_print = [k for k in sorted(self.stats.keys()) if k <= now]
                for ts in keys_to_print:
                    stat = self.stats.pop(ts)
                    print(f"[{ts.strftime('%H:%M:%S')}] BUY: {stat['buy_qty']} ({stat['buy_count']} orders), "
                          f"SELL: {stat['sell_qty']} ({stat['sell_count']} orders)")
                sys.stdout.flush()

# === Shoonya WebSocket Handler ===
class ShoonyaApiHandler(NorenApi):
    def __init__(self, exchange: str, token: str, analyzer: MarketOrderAnalyzer):
        super().__init__(host='https://api.shoonya.com/NorenWClientTP/',
                         websocket='wss://api.shoonya.com/NorenWSTP/')
        self.trading_symbol = f"{exchange}|{token}"
        self.feed_opened = False
        self.running = threading.Event()
        self.running.set()
        self.analyzer = analyzer

    def login(self):
        with open("Cread.json", "r") as f:
            creds = json.load(f)
        user, pwd = creds["user_id"], creds["password"]
        access_token = open('session_token.txt', 'r').read().strip()
        self.set_session(user, pwd, access_token)

    def open_callback(self):
        self.feed_opened = True
        print('WebSocket Opened ... \n')

    def close_callback(self):
        self.feed_opened = False
        print("\nWebSocket closed gracefully.")

    def event_handler_feed_update(self, tick_data):
        if not self.running.is_set():
            return
        self.analyzer.process_tick(tick_data)

    def start(self):
        self.start_websocket(order_update_callback=None,
                             subscribe_callback=self.event_handler_feed_update,
                             socket_open_callback=self.open_callback,
                             socket_close_callback=self.close_callback)

        while not self.feed_opened:
            pass

        self.subscribe(self.trading_symbol)

    def stop_execution(self, *args):
        print("\nUnsubscribing and closing WebSocket...")
        self.running.clear()
        if self.feed_opened:
            self.unsubscribe(self.trading_symbol)
            self.close_websocket()
        print("Execution stopped gracefully.")

# === Keyboard Monitor Thread ===
def monitor_keyboard(api_handler):
    print("\nPress **Escape** to stop the script...")
    while api_handler.running.is_set():
        if keyboard.is_pressed("esc"):
            api_handler.stop_execution()
            break

# === Run Everything ===
if __name__ == "__main__":
    exchange = 'NSE'
    token = '19585'  # Change to your desired token

    analyzer = MarketOrderAnalyzer(interval_sec=1)  # or use 5 / 60 for 5s / 1min
    api_handler = ShoonyaApiHandler(exchange=exchange, token=token, analyzer=analyzer)
    api_handler.login()

    signal.signal(signal.SIGINT, api_handler.stop_execution)
    signal.signal(signal.SIGTERM, api_handler.stop_execution)

    websocket_thread = threading.Thread(target=api_handler.start, daemon=True)
    monitor_thread = threading.Thread(target=analyzer.print_and_clear_stats, daemon=True)

    websocket_thread.start()
    monitor_thread.start()

    monitor_keyboard(api_handler)
