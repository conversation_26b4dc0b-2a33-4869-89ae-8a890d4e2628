import signal
import sys
import json
import threading
import keyboard
import time
from datetime import datetime, timedelta
from collections import defaultdict
from NorenRestApiPy.NorenApi import NorenApi

# === Enhanced Market Order Analyzer with Higher High/Lower Low Detection ===
class MarketOrderAnalyzer:
    def __init__(self, interval_sec=60):
        self.interval = interval_sec
        self.last_volume = None
        self.last_tick = None
        self.previous_bp1 = None
        self.previous_sp1 = None
        self.previous_bq1 = None
        self.previous_sq1 = None
        self.previous_lp = None
        self.previous_tbq = None
        self.previous_tsq = None

        # Price tracking for higher high/lower low detection
        self.price_history = []
        self.max_lookback = 10  # Number of ticks to look back for HH/LL detection
        self.current_high = 0
        self.current_low = float('inf')

        self.stats = defaultdict(lambda: {
            'buy_qty': 0, 'sell_qty': 0,
            'buy_market_orders': 0, 'sell_market_orders': 0,
            'buy_on_higher_high': 0, 'sell_on_higher_high': 0,
            'buy_on_lower_low': 0, 'sell_on_lower_low': 0,
            'max_price': 0, 'min_price': float('inf'),
            'buy_at_max_price': 0, 'buy_at_min_price': 0,
            'sell_at_max_price': 0, 'sell_at_min_price': 0,
            'biggest_buy_qty': 0, 'biggest_buy_price': 0, 'biggest_buy_time': None,
            'biggest_sell_qty': 0, 'biggest_sell_price': 0, 'biggest_sell_time': None,
            'higher_highs': 0, 'lower_lows': 0
        })

        self.lock = threading.Lock()
        self.total_buy_qty = 0
        self.total_sell_qty = 0
        self.total_buy_market_orders = 0
        self.total_sell_market_orders = 0
        self.total_buy_on_hh = 0
        self.total_sell_on_hh = 0
        self.total_buy_on_ll = 0
        self.total_sell_on_ll = 0

        # Calculate first rounded interval
        now = datetime.now()
        self.next_interval = (now.replace(second=0, microsecond=0) + timedelta(minutes=1))
        print(f"⏰ First analysis interval: {self.next_interval - timedelta(minutes=1)} to {self.next_interval}")
        print(f"🎯 Analyzing: Buy/Sell Market Orders on Higher Highs & Lower Lows")

    def detect_higher_high_lower_low(self, current_price):
        """Detect if current price is making higher high or lower low"""
        is_higher_high = False
        is_lower_low = False

        if len(self.price_history) >= self.max_lookback:
            recent_high = max(self.price_history[-self.max_lookback:])
            recent_low = min(self.price_history[-self.max_lookback:])

            # Higher High: Current price > recent highest price
            if current_price > recent_high and current_price > self.current_high:
                is_higher_high = True
                self.current_high = current_price

            # Lower Low: Current price < recent lowest price
            if current_price < recent_low and current_price < self.current_low:
                is_lower_low = True
                self.current_low = current_price

        # Update price history
        self.price_history.append(current_price)
        if len(self.price_history) > self.max_lookback * 2:  # Keep double for better analysis
            self.price_history.pop(0)

        return is_higher_high, is_lower_low

    def process_tick(self, tick):
        try:
            # Convert timestamp to IST
            if "ft" in tick:
                ts = datetime.utcfromtimestamp(int(tick["ft"])) + timedelta(hours=5, minutes=30)
                tick["processed_time"] = ts
            else:
                tick["processed_time"] = datetime.now()

            # Convert numeric fields with proper error handling
            numeric_fields = ["lp", "v", "ltq", "bp1", "sp1", "bq1", "sq1", "tbq", "tsq"]
            for field in numeric_fields:
                if field in tick and tick[field]:
                    try:
                        tick[field] = float(tick[field])
                    except:
                        tick[field] = 0.0
                else:
                    tick[field] = 0.0

        except Exception as e:
            return

        with self.lock:
            current_volume = tick["v"]
            current_price = tick["lp"]
            last_trade_qty = tick["ltq"]
            current_time = tick["processed_time"]
            current_tbq = tick.get("tbq", 0)
            current_tsq = tick.get("tsq", 0)

            # Skip if invalid data
            if current_price <= 0 or last_trade_qty <= 0:
                return

            # Initialize on first tick
            if self.last_volume is None:
                self.last_volume = current_volume
                self.last_tick = tick
                self.previous_bp1 = tick["bp1"]
                self.previous_sp1 = tick["sp1"]
                self.previous_bq1 = tick["bq1"]
                self.previous_sq1 = tick["sq1"]
                self.previous_lp = current_price
                self.previous_tbq = current_tbq
                self.previous_tsq = current_tsq
                self.current_high = current_price
                self.current_low = current_price
                return

            # Detect Higher High / Lower Low
            is_higher_high, is_lower_low = self.detect_higher_high_lower_low(current_price)

            vol_diff = current_volume - self.last_volume

            # Only process if there's meaningful volume change
            if vol_diff <= 0 or abs(vol_diff - last_trade_qty) > 1:  # Allow small rounding differences
                self._update_previous_values(tick, current_price, current_tbq, current_tsq)
                return

            # Get current market data
            current_best_bid = tick["bp1"]
            current_best_ask = tick["sp1"]
            current_bid_qty = tick["bq1"]
            current_ask_qty = tick["sq1"]

            # ENHANCED MARKET ORDER DETECTION LOGIC
            is_buy = False
            is_sell = False

            # Method 1: Price and liquidity based detection
            if (current_price >= current_best_ask and
                current_ask_qty < self.previous_sq1 and
                current_price >= self.previous_lp):
                is_buy = True

            elif (current_price <= current_best_bid and
                  current_bid_qty < self.previous_bq1 and
                  current_price <= self.previous_lp):
                is_sell = True

            # Method 2: Total buy/sell quantity changes (if available)
            if not is_buy and not is_sell and self.previous_tbq is not None:
                tbq_increase = current_tbq - self.previous_tbq
                tsq_increase = current_tsq - self.previous_tsq

                if tbq_increase > tsq_increase and tbq_increase > 0:
                    is_buy = True
                elif tsq_increase > tbq_increase and tsq_increase > 0:
                    is_sell = True

            # Method 3: Additional confirmation for large trades
            if not is_buy and not is_sell:
                if (current_price > self.previous_lp and
                    current_ask_qty < self.previous_sq1):
                    is_buy = True
                elif (current_price < self.previous_lp and
                      current_bid_qty < self.previous_bq1):
                    is_sell = True

            interval_key = self.next_interval - timedelta(minutes=1)
            stat = self.stats[interval_key]

            # Update HH/LL counters
            if is_higher_high:
                stat['higher_highs'] += 1
            if is_lower_low:
                stat['lower_lows'] += 1

            if is_buy:
                stat['buy_qty'] += last_trade_qty
                stat['buy_market_orders'] += 1

                # Track buy orders on Higher High / Lower Low
                if is_higher_high:
                    stat['buy_on_higher_high'] += last_trade_qty
                    print(f"🚀 BUY on HIGHER HIGH: {last_trade_qty:5.0f} @ {current_price:7.2f}")
                if is_lower_low:
                    stat['buy_on_lower_low'] += last_trade_qty
                    print(f"📈 BUY on LOWER LOW: {last_trade_qty:5.0f} @ {current_price:7.2f}")

                # Update price extremes
                if current_price > stat['max_price']:
                    stat['max_price'] = current_price
                if current_price < stat['min_price']:
                    stat['min_price'] = current_price

                # Update quantities at price extremes
                if current_price == stat['max_price']:
                    stat['buy_at_max_price'] += last_trade_qty
                if current_price == stat['min_price']:
                    stat['buy_at_min_price'] += last_trade_qty

                # Track biggest buy order
                if last_trade_qty > stat['biggest_buy_qty']:
                    stat['biggest_buy_qty'] = last_trade_qty
                    stat['biggest_buy_price'] = current_price
                    stat['biggest_buy_time'] = current_time

                symbol = tick.get('tsym', 'BSE-EQ')
                hh_ll_indicator = "🚀HH" if is_higher_high else ("📈LL" if is_lower_low else "")
                print(f"🟢 BUY: {last_trade_qty:5.0f} @ {current_price:7.2f} | {symbol} {hh_ll_indicator}")

            elif is_sell:
                stat['sell_qty'] += last_trade_qty
                stat['sell_market_orders'] += 1

                # Track sell orders on Higher High / Lower Low
                if is_higher_high:
                    stat['sell_on_higher_high'] += last_trade_qty
                    print(f"🔻 SELL on HIGHER HIGH: {last_trade_qty:5.0f} @ {current_price:7.2f}")
                if is_lower_low:
                    stat['sell_on_lower_low'] += last_trade_qty
                    print(f"📉 SELL on LOWER LOW: {last_trade_qty:5.0f} @ {current_price:7.2f}")

                # Update price extremes
                if current_price > stat['max_price']:
                    stat['max_price'] = current_price
                if current_price < stat['min_price']:
                    stat['min_price'] = current_price

                # Update quantities at price extremes
                if current_price == stat['max_price']:
                    stat['sell_at_max_price'] += last_trade_qty
                if current_price == stat['min_price']:
                    stat['sell_at_min_price'] += last_trade_qty

                # Track biggest sell order
                if last_trade_qty > stat['biggest_sell_qty']:
                    stat['biggest_sell_qty'] = last_trade_qty
                    stat['biggest_sell_price'] = current_price
                    stat['biggest_sell_time'] = current_time

                symbol = tick.get('tsym', 'BSE-EQ')
                hh_ll_indicator = "🚀HH" if is_higher_high else ("📉LL" if is_lower_low else "")
                print(f"🔴 SELL: {last_trade_qty:5.0f} @ {current_price:7.2f} | {symbol} {hh_ll_indicator}")

            # Update previous values
            self._update_previous_values(tick, current_price, current_tbq, current_tsq)

    def _update_previous_values(self, tick, current_price, current_tbq, current_tsq):
        """Update previous values for next comparison"""
        self.last_tick = tick
        self.last_volume = tick["v"]
        self.previous_bp1 = tick["bp1"]
        self.previous_sp1 = tick["sp1"]
        self.previous_bq1 = tick["bq1"]
        self.previous_sq1 = tick["sq1"]
        self.previous_lp = current_price
        self.previous_tbq = current_tbq
        self.previous_tsq = current_tsq

    def print_and_clear_stats(self):
        while True:
            now = datetime.now()
            if now >= self.next_interval:
                interval_start = self.next_interval - timedelta(minutes=1)
                interval_end = self.next_interval
                
                with self.lock:
                    stat = self.stats.pop(interval_start, None)
                    if stat:
                        # Update cumulative totals
                        self.total_buy_qty += stat['buy_qty']
                        self.total_sell_qty += stat['sell_qty']
                        self.total_buy_market_orders += stat['buy_market_orders']
                        self.total_sell_market_orders += stat['sell_market_orders']
                        self.total_buy_on_hh += stat['buy_on_higher_high']
                        self.total_sell_on_hh += stat['sell_on_higher_high']
                        self.total_buy_on_ll += stat['buy_on_lower_low']
                        self.total_sell_on_ll += stat['sell_on_lower_low']

                        # Display analysis
                        print(f"\n{'='*90}")
                        print(f"📊 ENHANCED MARKET ORDER ANALYSIS - HIGHER HIGH/LOWER LOW")
                        print(f"⏰ {interval_start.strftime('%H:%M:%S')} → {interval_end.strftime('%H:%M:%S')}")
                        print(f"{'='*90}")

                        # Current interval market orders
                        print(f"\n📦 CURRENT INTERVAL MARKET ORDERS:")
                        print(f"🟢 Buy Orders:    {stat['buy_market_orders']:>6.0f} | Quantity: {stat['buy_qty']:>8.0f}")
                        print(f"🔴 Sell Orders:   {stat['sell_market_orders']:>6.0f} | Quantity: {stat['sell_qty']:>8.0f}")

                        # Higher High / Lower Low Analysis
                        print(f"\n🎯 HIGHER HIGH / LOWER LOW ANALYSIS:")
                        print(f"🚀 Higher Highs:  {stat['higher_highs']:>6.0f}")
                        print(f"📉 Lower Lows:    {stat['lower_lows']:>6.0f}")
                        print(f"🟢 Buy on HH:     {stat['buy_on_higher_high']:>8.0f}")
                        print(f"🔴 Sell on HH:    {stat['sell_on_higher_high']:>8.0f}")
                        print(f"🟢 Buy on LL:     {stat['buy_on_lower_low']:>8.0f}")
                        print(f"🔴 Sell on LL:    {stat['sell_on_lower_low']:>8.0f}")

                        # Cumulative totals
                        print(f"\n📈 CUMULATIVE TOTALS:")
                        print(f"🟢 Total Buy Orders:   {self.total_buy_market_orders:>6.0f} | Quantity: {self.total_buy_qty:>8.0f}")
                        print(f"🔴 Total Sell Orders:  {self.total_sell_market_orders:>6.0f} | Quantity: {self.total_sell_qty:>8.0f}")
                        print(f"🚀 Total Buy on HH:    {self.total_buy_on_hh:>8.0f}")
                        print(f"🔻 Total Sell on HH:   {self.total_sell_on_hh:>8.0f}")
                        print(f"📈 Total Buy on LL:    {self.total_buy_on_ll:>8.0f}")
                        print(f"📉 Total Sell on LL:   {self.total_sell_on_ll:>8.0f}")

                        # Price extremes with quantities
                        print(f"\n💰 PRICE EXTREMES:")
                        if stat['max_price'] > 0:
                            print(f"📈 Max Price: {stat['max_price']:>8.2f}")
                            print(f"   → Buy Qty:  {stat['buy_at_max_price']:>8.0f}")
                            print(f"   → Sell Qty: {stat['sell_at_max_price']:>8.0f}")

                        if stat['min_price'] != float('inf'):
                            print(f"📉 Min Price: {stat['min_price']:>8.2f}")
                            print(f"   → Buy Qty:  {stat['buy_at_min_price']:>8.0f}")
                            print(f"   → Sell Qty: {stat['sell_at_min_price']:>8.0f}")

                        # Biggest orders
                        print(f"\n🏆 BIGGEST ORDERS:")
                        if stat['biggest_buy_qty'] > 0:
                            time_str = stat['biggest_buy_time'].strftime('%H:%M:%S') if stat['biggest_buy_time'] else 'N/A'
                            print(f"🟢 Biggest Buy:  {stat['biggest_buy_qty']:>6.0f} @ {stat['biggest_buy_price']:>7.2f} at {time_str}")

                        if stat['biggest_sell_qty'] > 0:
                            time_str = stat['biggest_sell_time'].strftime('%H:%M:%S') if stat['biggest_sell_time'] else 'N/A'
                            print(f"🔴 Biggest Sell: {stat['biggest_sell_qty']:>6.0f} @ {stat['biggest_sell_price']:>7.2f} at {time_str}")

                        print(f"{'='*90}\n")
                    else:
                        print(f"\n[{interval_start.strftime('%H:%M:%S')} → {interval_end.strftime('%H:%M:%S')}] No market orders detected.")
                
                # Schedule next interval
                self.next_interval += timedelta(minutes=1)
                print(f"⏰ Next analysis: {self.next_interval - timedelta(minutes=1)} to {self.next_interval}")
            
            time.sleep(0.1)

# === Shoonya WebSocket Handler ===
class ShoonyaApiHandler(NorenApi):
    def __init__(self, exchange: str, token: str, analyzer: MarketOrderAnalyzer):
        super().__init__(host='https://api.shoonya.com/NorenWClientTP/',
                         websocket='wss://api.shoonya.com/NorenWSTP/')
        self.trading_symbol = f"{exchange}|{token}"
        self.feed_opened = False
        self.running = threading.Event()
        self.running.set()
        self.analyzer = analyzer

    def login(self):
        with open("Cread.json", "r") as f:
            creds = json.load(f)
        user, pwd = creds["user_id"], creds["password"]
        access_token = open('session_token.txt', 'r').read().strip()
        self.set_session(user, pwd, access_token)
        print("✅ Login successful")

    def open_callback(self):
        self.feed_opened = True
        print('✅ WebSocket Connected')

    def close_callback(self):
        self.feed_opened = False
        print("🔌 WebSocket closed")

    def event_handler_feed_update(self, tick_data):
        if not self.running.is_set():
            return
        
        # Fix for "Unknown" symbol - ensure we have proper tick data
        if not tick_data.get('tsym'):
            tick_data['tsym'] = 'BSE-EQ'
        
        # Display live tick with proper formatting
        symbol = tick_data.get('tsym', 'BSE-EQ')
        ltp = tick_data.get('lp', 0)
        volume = tick_data.get('v', 0)
        print(f"📡 {symbol} | LTP: {ltp} | Vol: {volume}", end='\r')
        
        self.analyzer.process_tick(tick_data)

    def start(self):
        self.start_websocket(order_update_callback=None,
                             subscribe_callback=self.event_handler_feed_update,
                             socket_open_callback=self.open_callback,
                             socket_close_callback=self.close_callback)
        
        # Wait for connection
        timeout = 10
        start_time = time.time()
        while not self.feed_opened and (time.time() - start_time) < timeout:
            time.sleep(0.1)

        if not self.feed_opened:
            print("❌ WebSocket connection timeout")
            return

        self.subscribe(self.trading_symbol)
        print(f"✅ Subscribed to: {self.trading_symbol}")

    def stop_execution(self, *args):
        print("\n🛑 Unsubscribing and closing WebSocket...")
        self.running.clear()
        if self.feed_opened:
            self.unsubscribe(self.trading_symbol)
            self.close_websocket()
        print("✅ Execution stopped gracefully.")
        sys.exit(0)

# === Keyboard Monitor Thread ===
def monitor_keyboard(api_handler):
    print("\nPress **ESC** to stop the script...")
    while api_handler.running.is_set():
        if keyboard.is_pressed("esc"):
            api_handler.stop_execution()
            break
        time.sleep(0.1)

# === Run Everything ===
if __name__ == "__main__":
    exchange = 'NSE'
    token = '19585'  # BSE-EQ token

    print("🚀 Starting Enhanced Market Order Analyzer...")
    print("📊 Using: bp1, bq1, sp1, sq1 for accurate detection")
    print("⏰ Rounded 1-minute intervals")
    print("🎯 Proper market order logic: Buy=Price↑+AskQty↓, Sell=Price↓+BidQty↓")
    print(f"{'='*60}")
    
    analyzer = MarketOrderAnalyzer(interval_sec=60)
    api_handler = ShoonyaApiHandler(exchange=exchange, token=token, analyzer=analyzer)
    api_handler.login()

    signal.signal(signal.SIGINT, api_handler.stop_execution)
    signal.signal(signal.SIGTERM, api_handler.stop_execution)

    websocket_thread = threading.Thread(target=api_handler.start, daemon=True)
    stats_thread = threading.Thread(target=analyzer.print_and_clear_stats, daemon=True)

    websocket_thread.start()
    stats_thread.start()

    monitor_keyboard(api_handler)