import signal
import sys
import json
import threading
import keyboard
import time
from datetime import datetime, timedelta
from collections import defaultdict
from NorenRestApiPy.NorenApi import NorenApi

# === Enhanced Market Order Analyzer ===
class MarketOrderAnalyzer:
    def __init__(self, interval_sec=60):
        self.interval = interval_sec
        self.last_volume = None
        self.last_tick = None
        self.previous_bp1 = None
        self.previous_sp1 = None
        self.previous_bq1 = None
        self.previous_sq1 = None
        self.previous_lp = None
        
        self.stats = defaultdict(lambda: {
            'buy_qty': 0, 'sell_qty': 0,
            'max_price': 0, 'min_price': float('inf'),
            'buy_at_max_price': 0, 'buy_at_min_price': 0,
            'sell_at_max_price': 0, 'sell_at_min_price': 0,
            'biggest_buy_qty': 0, 'biggest_buy_price': 0, 'biggest_buy_time': None,
            'biggest_sell_qty': 0, 'biggest_sell_price': 0, 'biggest_sell_time': None
        })
        
        self.lock = threading.Lock()
        self.total_buy_qty = 0
        self.total_sell_qty = 0
        
        # Calculate first rounded interval
        now = datetime.now()
        self.next_interval = (now.replace(second=0, microsecond=0) + timedelta(minutes=1))
        print(f"⏰ First analysis interval: {(self.next_interval - timedelta(minutes=1)).strftime('%H:%M:%S')} to {self.next_interval.strftime('%H:%M:%S')}")

    def process_tick(self, tick):
        try:
            # Convert timestamp to IST
            if "ft" in tick:
                ts = datetime.utcfromtimestamp(int(tick["ft"])) + timedelta(hours=5, minutes=30)
                tick["processed_time"] = ts
            else:
                tick["processed_time"] = datetime.now()
            
            # Convert numeric fields with proper error handling
            numeric_fields = ["lp", "v", "ltq", "bp1", "sp1", "bq1", "sq1"]
            for field in numeric_fields:
                if field in tick and tick[field]:
                    try:
                        tick[field] = float(tick[field])
                    except:
                        tick[field] = 0.0
                else:
                    tick[field] = 0.0
            
        except Exception as e:
            return

        with self.lock:
            current_volume = tick["v"]
            current_price = tick["lp"]
            last_trade_qty = tick["ltq"]
            current_time = tick["processed_time"]
            
            # Skip if invalid data
            if current_price <= 0 or last_trade_qty <= 0:
                return

            # Initialize on first tick
            if self.last_volume is None:
                self.last_volume = current_volume
                self.last_tick = tick
                self.previous_bp1 = tick["bp1"]
                self.previous_sp1 = tick["sp1"]
                self.previous_bq1 = tick["bq1"]
                self.previous_sq1 = tick["sq1"]
                self.previous_lp = current_price
                return

            vol_diff = current_volume - self.last_volume
            
            # Process all valid ticks, not just volume changes
            # This allows us to detect price movements even without volume changes
            if vol_diff < 0:  # Only skip if volume decreased (data error)
                self._update_previous_values(tick, current_price)
                return

            # Get current market data
            current_best_bid = tick["bp1"]
            current_best_ask = tick["sp1"]
            current_bid_qty = tick["bq1"]
            current_ask_qty = tick["sq1"]

            # SIMPLIFIED BUT EFFECTIVE MARKET ORDER DETECTION
            is_buy = False
            is_sell = False

            # METHOD 1: Trade at ask price = Buy market order
            if current_price >= current_best_ask:
                is_buy = True
            # METHOD 2: Trade at bid price = Sell market order  
            elif current_price <= current_best_bid:
                is_sell = True
            # METHOD 3: Price moved up significantly = Buy market order
            elif current_price > self.previous_lp + 0.1:  # 10 paisa movement
                is_buy = True
            # METHOD 4: Price moved down significantly = Sell market order
            elif current_price < self.previous_lp - 0.1:  # 10 paisa movement
                is_sell = True
            # METHOD 5: Large trade with bid/ask quantity change
            elif last_trade_qty > 100:  # Large trade
                if current_ask_qty < self.previous_sq1:
                    is_buy = True
                elif current_bid_qty < self.previous_bq1:
                    is_sell = True

            interval_key = self.next_interval - timedelta(minutes=1)
            stat = self.stats[interval_key]

            if is_buy:
                stat['buy_qty'] += last_trade_qty
                
                # Update price extremes
                if current_price > stat['max_price']:
                    stat['max_price'] = current_price
                    stat['buy_at_max_price'] = last_trade_qty
                    stat['sell_at_max_price'] = 0
                elif current_price == stat['max_price']:
                    stat['buy_at_max_price'] += last_trade_qty
                
                if current_price < stat['min_price']:
                    stat['min_price'] = current_price
                    stat['buy_at_min_price'] = last_trade_qty
                    stat['sell_at_min_price'] = 0
                elif current_price == stat['min_price']:
                    stat['buy_at_min_price'] += last_trade_qty
                
                # Track biggest buy order
                if last_trade_qty > stat['biggest_buy_qty']:
                    stat['biggest_buy_qty'] = last_trade_qty
                    stat['biggest_buy_price'] = current_price
                    stat['biggest_buy_time'] = current_time

                symbol = tick.get('tsym', 'BSE-EQ')
                print(f"🟢 BUY: {last_trade_qty:5.0f} @ {current_price:7.2f} | Ask: {current_best_ask:.2f}")

            elif is_sell:
                stat['sell_qty'] += last_trade_qty
                
                # Update price extremes
                if current_price > stat['max_price']:
                    stat['max_price'] = current_price
                    stat['buy_at_max_price'] = 0
                    stat['sell_at_max_price'] = last_trade_qty
                elif current_price == stat['max_price']:
                    stat['sell_at_max_price'] += last_trade_qty
                
                if current_price < stat['min_price']:
                    stat['min_price'] = current_price
                    stat['buy_at_min_price'] = 0
                    stat['sell_at_min_price'] = last_trade_qty
                elif current_price == stat['min_price']:
                    stat['sell_at_min_price'] += last_trade_qty
                
                # Track biggest sell order
                if last_trade_qty > stat['biggest_sell_qty']:
                    stat['biggest_sell_qty'] = last_trade_qty
                    stat['biggest_sell_price'] = current_price
                    stat['biggest_sell_time'] = current_time

                symbol = tick.get('tsym', 'BSE-EQ')
                print(f"🔴 SELL: {last_trade_qty:5.0f} @ {current_price:7.2f} | Bid: {current_best_bid:.2f}")

            # Update previous values
            self._update_previous_values(tick, current_price)

    def _update_previous_values(self, tick, current_price):
        """Update previous values for next comparison"""
        self.last_tick = tick
        self.last_volume = tick["v"]
        self.previous_bp1 = tick["bp1"]
        self.previous_sp1 = tick["sp1"]
        self.previous_bq1 = tick["bq1"]
        self.previous_sq1 = tick["sq1"]
        self.previous_lp = current_price

    def print_and_clear_stats(self):
        while True:
            now = datetime.now()
            if now >= self.next_interval:
                interval_start = self.next_interval - timedelta(minutes=1)
                interval_end = self.next_interval
                
                with self.lock:
                    stat = self.stats.pop(interval_start, None)
                    if stat and (stat['buy_qty'] > 0 or stat['sell_qty'] > 0):
                        # Update cumulative totals
                        self.total_buy_qty += stat['buy_qty']
                        self.total_sell_qty += stat['sell_qty']
                        
                        # Display analysis
                        print(f"\n{'='*80}")
                        print(f"📊 MARKET ORDER ANALYSIS")
                        print(f"⏰ {interval_start.strftime('%H:%M:%S')} → {interval_end.strftime('%H:%M:%S')}")
                        print(f"{'='*80}")
                        
                        # Current interval quantities
                        print(f"\n📦 QUANTITIES:")
                        print(f"🟢 Buy:  {stat['buy_qty']:>8.0f}")
                        print(f"🔴 Sell: {stat['sell_qty']:>8.0f}")
                        
                        # Cumulative totals
                        print(f"\n📈 CUMULATIVE:")
                        print(f"🟢 Total Buy:  {self.total_buy_qty:>8.0f}")
                        print(f"🔴 Total Sell: {self.total_sell_qty:>8.0f}")
                        
                        # Price extremes with quantities
                        print(f"\n💰 PRICE EXTREMES:")
                        if stat['max_price'] > 0:
                            print(f"📈 Max: {stat['max_price']:>8.2f}")
                            print(f"   → Buy:  {stat['buy_at_max_price']:>6.0f}")
                            print(f"   → Sell: {stat['sell_at_max_price']:>6.0f}")
                        
                        if stat['min_price'] != float('inf'):
                            print(f"📉 Min: {stat['min_price']:>8.2f}")
                            print(f"   → Buy:  {stat['buy_at_min_price']:>6.0f}")
                            print(f"   → Sell: {stat['sell_at_min_price']:>6.0f}")
                        
                        # Biggest orders
                        print(f"\n🏆 BIGGEST ORDERS:")
                        if stat['biggest_buy_qty'] > 0:
                            time_str = stat['biggest_buy_time'].strftime('%H:%M:%S') if stat['biggest_buy_time'] else 'N/A'
                            print(f"🟢 Buy:  {stat['biggest_buy_qty']:>6.0f} @ {stat['biggest_buy_price']:>7.2f} ({time_str})")
                        
                        if stat['biggest_sell_qty'] > 0:
                            time_str = stat['biggest_sell_time'].strftime('%H:%M:%S') if stat['biggest_sell_time'] else 'N/A'
                            print(f"🔴 Sell: {stat['biggest_sell_qty']:>6.0f} @ {stat['biggest_sell_price']:>7.2f} ({time_str})")
                        
                        print(f"{'='*80}\n")
                    else:
                        print(f"\n💤 {interval_start.strftime('%H:%M:%S')} → {interval_end.strftime('%H:%M:%S')}: No market orders")
                
                # Schedule next interval
                self.next_interval += timedelta(minutes=1)
                print(f"⏰ Next: {self.next_interval.strftime('%H:%M:%S')}")
            
            time.sleep(0.1)

# === Shoonya WebSocket Handler ===
class ShoonyaApiHandler(NorenApi):
    def __init__(self, exchange: str, token: str, analyzer: MarketOrderAnalyzer):
        super().__init__(host='https://api.shoonya.com/NorenWClientTP/',
                         websocket='wss://api.shoonya.com/NorenWSTP/')
        self.trading_symbol = f"{exchange}|{token}"
        self.feed_opened = False
        self.running = threading.Event()
        self.running.set()
        self.analyzer = analyzer

    def login(self):
        with open("Cread.json", "r") as f:
            creds = json.load(f)
        user, pwd = creds["user_id"], creds["password"]
        access_token = open('session_token.txt', 'r').read().strip()
        self.set_session(user, pwd, access_token)
        print("✅ Login successful")

    def open_callback(self):
        self.feed_opened = True
        print('✅ WebSocket Connected')

    def close_callback(self):
        self.feed_opened = False
        print("🔌 WebSocket closed")

    def event_handler_feed_update(self, tick_data):
        if not self.running.is_set():
            return

        # DATA VALIDATION AND CLEANING
        try:
            # Fix symbol
            if not tick_data.get('tsym'):
                tick_data['tsym'] = 'BSE-EQ'

            # Validate and clean LTP
            ltp = tick_data.get('lp', 0)
            if ltp is None or ltp == '' or ltp == 0:
                return  # Skip invalid ticks

            try:
                ltp = float(ltp)
                if ltp <= 0 or ltp > 100000:  # Invalid price range
                    return
            except (ValueError, TypeError):
                return

            # Validate and clean Volume
            volume = tick_data.get('v', 0)
            if volume is None or volume == '':
                volume = 0

            try:
                volume = float(volume)
                if volume < 0 or volume > 1e9:  # Volume too large (> 1 billion)
                    return
            except (ValueError, TypeError):
                volume = 0

            # Update cleaned data
            tick_data['lp'] = ltp
            tick_data['v'] = volume

            # Display live tick with validation and more info
            symbol = tick_data.get('tsym', 'BSE-EQ')
            ltq = tick_data.get('ltq', 0)
            bp1 = tick_data.get('bp1', 0)
            sp1 = tick_data.get('sp1', 0)

            # Show more detailed tick info
            print(f"📡 {symbol} | LTP: {ltp:7.2f} | Vol: {volume:>10.0f} | LTQ: {ltq:>5.0f} | Bid: {bp1:6.2f} | Ask: {sp1:6.2f}", end='\r')

            # Only process if we have valid data
            if ltp > 0 and ltq > 0:
                self.analyzer.process_tick(tick_data)
            elif ltp > 0:
                # Still process for price tracking even without trade quantity
                tick_data['ltq'] = 1  # Minimal quantity for processing
                self.analyzer.process_tick(tick_data)

        except Exception as e:
            print(f"\n❌ Error in tick handler: {e}")
            return

    def start(self):
        self.start_websocket(order_update_callback=None,
                             subscribe_callback=self.event_handler_feed_update,
                             socket_open_callback=self.open_callback,
                             socket_close_callback=self.close_callback)
        
        # Wait for connection
        timeout = 10
        start_time = time.time()
        while not self.feed_opened and (time.time() - start_time) < timeout:
            time.sleep(0.1)

        if not self.feed_opened:
            print("❌ WebSocket connection timeout")
            return

        self.subscribe(self.trading_symbol)
        print(f"✅ Subscribed to: {self.trading_symbol}")

    def stop_execution(self, *args):
        print("\n🛑 Unsubscribing and closing WebSocket...")
        self.running.clear()
        if self.feed_opened:
            self.unsubscribe(self.trading_symbol)
            self.close_websocket()
        print("✅ Execution stopped gracefully.")
        sys.exit(0)

# === Keyboard Monitor Thread ===
def monitor_keyboard(api_handler):
    print("\nPress **ESC** to stop the script...")
    while api_handler.running.is_set():
        if keyboard.is_pressed("esc"):
            api_handler.stop_execution()
            break
        time.sleep(0.1)

# === Run Everything ===
if __name__ == "__main__":
    exchange = 'NSE'
    token = '19585'  # BSE-EQ token

    print("🚀 Starting Enhanced Market Order Analyzer...")
    print("📊 Using: bp1, bq1, sp1, sq1 for accurate detection")
    print("⏰ Rounded 1-minute intervals")
    print("🎯 SIMPLIFIED LOGIC: Trade@Ask=Buy, Trade@Bid=Sell, PriceMove=MarketOrder")
    print(f"{'='*60}")
    
    analyzer = MarketOrderAnalyzer(interval_sec=60)
    api_handler = ShoonyaApiHandler(exchange=exchange, token=token, analyzer=analyzer)
    api_handler.login()

    signal.signal(signal.SIGINT, api_handler.stop_execution)
    signal.signal(signal.SIGTERM, api_handler.stop_execution)

    websocket_thread = threading.Thread(target=api_handler.start, daemon=True)
    stats_thread = threading.Thread(target=analyzer.print_and_clear_stats, daemon=True)

    websocket_thread.start()
    stats_thread.start()

    monitor_keyboard(api_handler)