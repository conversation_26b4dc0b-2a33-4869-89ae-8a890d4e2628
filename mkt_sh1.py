import signal
import sys
import json
import threading
import keyboard
import time
from datetime import datetime, timedelta
from collections import defaultdict
from NorenRestApiPy.NorenApi import NorenApi

# === Real-time Market Order Inference ===
class MarketOrderAnalyzer:
    def __init__(self, interval_sec=60):
        self.interval = interval_sec
        self.last_volume = None
        self.last_tick = None
        self.stats = defaultdict(lambda: {
            'buy_qty': 0, 'sell_qty': 0,
            'buy_count': 0, 'sell_count': 0,
            'max_buy_price': 0, 'min_buy_price': float('inf'),
            'max_sell_price': 0, 'min_sell_price': float('inf'),
            'buy_at_max_price': 0, 'buy_at_min_price': 0,
            'sell_at_max_price': 0, 'sell_at_min_price': 0
        })
        self.lock = threading.Lock()
        self.total_buy = 0
        self.total_sell = 0
        now = datetime.now()
        self.next_interval = (now.replace(second=0, microsecond=0) + timedelta(minutes=1))

    def process_tick(self, tick):
        try:
            ts = datetime.utcfromtimestamp(int(tick["ft"])) + timedelta(hours=5, minutes=30)
            tick["ft"] = ts
            tick["lp"] = float(tick["lp"])
            tick["v"] = float(tick["v"])
            tick["tbq"] = float(tick.get("tbq", 0))
            tick["tsq"] = float(tick.get("tsq", 0))
            tick["bp1"] = float(tick.get("bp1", 0))
            tick["sp1"] = float(tick.get("sp1", 0))
        except Exception:
            return

        with self.lock:
            if self.last_volume is None:
                self.last_volume = tick["v"]
                self.last_tick = tick
                return

            vol_diff = tick["v"] - self.last_volume
            if vol_diff <= 0:
                self.last_tick = tick
                self.last_volume = tick["v"]
                return

            is_buy = False
            is_sell = False

            if tick['lp'] > self.last_tick['lp']:
                is_buy = True
            elif tick['lp'] < self.last_tick['lp']:
                is_sell = True
            else:
                if tick['tbq'] > self.last_tick['tbq']:
                    is_buy = True
                if tick['tsq'] > self.last_tick['tsq']:
                    is_sell = True

            interval_key = self.next_interval - timedelta(minutes=1)
            stat = self.stats[interval_key]

            if is_buy:
                stat['buy_qty'] += vol_diff
                stat['buy_count'] += 1
                if tick['lp'] > stat['max_buy_price']:
                    stat['max_buy_price'] = tick['lp']
                    stat['buy_at_max_price'] = vol_diff
                elif tick['lp'] == stat['max_buy_price']:
                    stat['buy_at_max_price'] += vol_diff

                if tick['lp'] < stat['min_buy_price']:
                    stat['min_buy_price'] = tick['lp']
                    stat['buy_at_min_price'] = vol_diff
                elif tick['lp'] == stat['min_buy_price']:
                    stat['buy_at_min_price'] += vol_diff

            if is_sell:
                stat['sell_qty'] += vol_diff
                stat['sell_count'] += 1
                if tick['lp'] > stat['max_sell_price']:
                    stat['max_sell_price'] = tick['lp']
                    stat['sell_at_max_price'] = vol_diff
                elif tick['lp'] == stat['max_sell_price']:
                    stat['sell_at_max_price'] += vol_diff

                if tick['lp'] < stat['min_sell_price']:
                    stat['min_sell_price'] = tick['lp']
                    stat['sell_at_min_price'] = vol_diff
                elif tick['lp'] == stat['min_sell_price']:
                    stat['sell_at_min_price'] += vol_diff

            self.last_tick = tick
            self.last_volume = tick["v"]

    def print_and_clear_stats(self):
        while True:
            now = datetime.now()
            if now >= self.next_interval:
                interval_start = self.next_interval - timedelta(minutes=1)
                interval_end = self.next_interval
                with self.lock:
                    stat = self.stats.pop(interval_start, None)
                    if stat:
                        self.total_buy += stat['buy_qty']
                        self.total_sell += stat['sell_qty']
                        print(f"[{interval_start.strftime('%H:%M:%S')} → {interval_end.strftime('%H:%M:%S')}]")
                        print(f"  BUY : Qty={stat['buy_qty']} ({stat['buy_count']} orders), "
                              f"Max@{stat['max_buy_price']} ({stat['buy_at_max_price']}), "
                              f"Min@{stat['min_buy_price']} ({stat['buy_at_min_price']})")
                        print(f"  SELL: Qty={stat['sell_qty']} ({stat['sell_count']} orders), "
                              f"Max@{stat['max_sell_price']} ({stat['sell_at_max_price']}), "
                              f"Min@{stat['min_sell_price']} ({stat['sell_at_min_price']})")
                        print(f"  Total cumulative BUY: {self.total_buy}, SELL: {self.total_sell}")
                        print("-"*60)
                    else:
                        print(f"[{interval_start.strftime('%H:%M:%S')} → {interval_end.strftime('%H:%M:%S')}] No market orders.")
                self.next_interval += timedelta(minutes=1)
            time.sleep(0.2)

# === Shoonya WebSocket Handler ===
class ShoonyaApiHandler(NorenApi):
    def __init__(self, exchange: str, token: str, analyzer: MarketOrderAnalyzer):
        super().__init__(host='https://api.shoonya.com/NorenWClientTP/',
                         websocket='wss://api.shoonya.com/NorenWSTP/')
        self.trading_symbol = f"{exchange}|{token}"
        self.feed_opened = False
        self.running = threading.Event()
        self.running.set()
        self.analyzer = analyzer

    def login(self):
        with open("Cread.json", "r") as f:
            creds = json.load(f)
        user, pwd = creds["user_id"], creds["password"]
        access_token = open('session_token.txt', 'r').read().strip()
        self.set_session(user, pwd, access_token)

    def open_callback(self):
        self.feed_opened = True
        print('WebSocket Opened ... \n')

    def close_callback(self):
        self.feed_opened = False
        print("\nWebSocket closed gracefully.")

    def event_handler_feed_update(self, tick_data):
        if not self.running.is_set():
            return
        self.analyzer.process_tick(tick_data)

    def start(self):
        self.start_websocket(order_update_callback=None,
                             subscribe_callback=self.event_handler_feed_update,
                             socket_open_callback=self.open_callback,
                             socket_close_callback=self.close_callback)
        while not self.feed_opened:
            pass
        self.subscribe(self.trading_symbol)

    def stop_execution(self, *args):
        print("\nUnsubscribing and closing WebSocket...")
        self.running.clear()
        if self.feed_opened:
            self.unsubscribe(self.trading_symbol)
            self.close_websocket()
        print("Execution stopped gracefully.")

# === Keyboard Monitor Thread ===
def monitor_keyboard(api_handler):
    print("\nPress **Escape** to stop the script...")
    while api_handler.running.is_set():
        if keyboard.is_pressed("esc"):
            api_handler.stop_execution()
            break

# === Run Everything ===
if __name__ == "__main__":
    exchange = 'NSE'
    token = '19585'  # Change to your desired token

    analyzer = MarketOrderAnalyzer(interval_sec=60)  # 1-minute interval
    api_handler = ShoonyaApiHandler(exchange=exchange, token=token, analyzer=analyzer)
    api_handler.login()

    signal.signal(signal.SIGINT, api_handler.stop_execution)
    signal.signal(signal.SIGTERM, api_handler.stop_execution)

    websocket_thread = threading.Thread(target=api_handler.start, daemon=True)
    monitor_thread = threading.Thread(target=analyzer.print_and_clear_stats, daemon=True)

    websocket_thread.start()
    monitor_thread.start()

    monitor_keyboard(api_handler)
