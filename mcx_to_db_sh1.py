# mcx_to_db_sh1.py
# this code fetches api call data and stores in database of mcx
from __future__ import annotations
import json
import time
from typing import List, Dict
from NorenRestApiPy.NorenApi import NorenApi
import sqlalchemy as db
import requests
import msvcrt
import sys
from datetime import datetime, time as dt_time
import pytz
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.orm import sessionmaker
import traceback
import logging
from urllib.parse import quote_plus
import os
from pyotp import TOTP
import timedelta

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('mcx_to_db.log')
    ]
)
logger = logging.getLogger(__name__)

# --- Load Credentials ---
try:
    with open('Cread.json', 'r') as fp:
        creds = json.load(fp)
    DB_HOST = creds.get('db_host', 'localhost')
    DB_PORT = creds.get('db_port', '5432')
    DB_NAME = creds.get('db_name', 'options_mcx_DB')
    DB_USER = creds.get('db_user', 'postgres')
    DB_PASS = creds.get('db_pass', 'Muni@555')
    DB_PASS_ENCODED = quote_plus(DB_PASS)
except Exception as e:
    logger.error(f"Cannot read Cread.json: {e}")
    sys.exit(1)

# --- Constants ---
MARKET_START_TIME = dt_time(8, 59, 55)
# MARKET_END_TIME = dt_time(15, 300)
MARKET_END_TIME = dt_time(23, 30, 0)
FETCH_INTERVAL_SECONDS = 5
IST = pytz.timezone('Asia/Kolkata')
TOKENS = [
    {'token': '455866', 'tsym': 'CRUDEOILM20OCT25', 'exchange': 'MCX'}
]
EXIT_AFTER_MARKET_CLOSE = False

# --- Shoonya Session Loader ---
class ShoonyaSessionLoader:
    REST_URL = "https://api.shoonya.com/NorenWClientTP/"
    WS_URL = "wss://api.shoonya.com/NorenWSTP/"

    def __init__(self, cred_file: str = "Cread.json", token_file: str = "session_token.txt"):
        self.cred_file = cred_file
        self.token_file = token_file
        self._api = None
        self._creds = None

    @property
    def api(self) -> NorenApi:
        return self._api

    def load(self) -> bool:
        try:
            if not os.path.exists(self.cred_file):
                logger.error(f"Cread.json not found at {self.cred_file}")
                return False

            with open(self.cred_file, "r") as fp:
                self._creds = json.load(fp)

            required_fields = ['user_id', 'password', 'app_key', 'imei', 'vendor_code', 'totp']
            missing = [field for field in required_fields if field not in self._creds]
            if missing:
                logger.error(f"Missing fields in Cread.json: {missing}")
                return False

            class _Api(NorenApi):
                def __init__(self_):
                    super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)

            self._api = _Api()

            # Try loading existing session token
            if os.path.exists(self.token_file):
                with open(self.token_file, "r") as fp:
                    token = fp.read().strip()
                self._api.set_session(
                    userid=self._creds["user_id"],
                    password=self._creds["password"],
                    usertoken=token
                )
                # Validate token with a test API call
                try:
                    self._api.get_limits()
                    logger.info("Existing session token validated successfully.")
                    return True
                except Exception as e:
                    logger.warning(f"Existing session token invalid: {e}. Attempting fresh login.")

            # Perform fresh login
            otp = TOTP(self._creds["totp"]).now().zfill(6)
            ret = self._api.login(
                userid=self._creds["user_id"],
                password=self._creds["password"],
                twoFA=otp,
                vendor_code=self._creds["vendor_code"],
                api_secret=self._creds["app_key"],
                imei=self._creds["imei"]
            )
            if ret is None or 'susertoken' not in ret:
                logger.error(f"Login failed. Response: {ret}")
                return False

            # Save new session token
            with open(self.token_file, 'w') as fp:
                fp.write(ret['susertoken'])
            logger.info("Shoonya session loaded successfully with new token.")
            return True
        except Exception as e:
            logger.error(f"Shoonya session setup failed: {e}\n{traceback.format_exc()}")
            return False

# --- Quote Fetcher ---
def fetch_quotes(api: NorenApi, tokens: List[Dict[str, str]], delay_ms: int = 200) -> Dict[str, dict]:
    quotes = {}
    for token_info in tokens:
        token = token_info['token']
        exchange = token_info['exchange']
        try:
            quote = api.get_quotes(exchange=exchange, token=token)
            if quote.get('stat') == 'Ok':
                quotes[token] = quote
                logger.info(f"Token {token} ({token_info['tsym']}) fetched.")
            else:
                logger.error(f"Token {token} failed: {quote.get('emsg', 'Unknown error')}")
        except Exception as e:
            logger.error(f"Token {token} failed: {e}")
        time.sleep(delay_ms / 1000.0)
    return quotes

# --- Network Check ---
def is_network_alive(max_retries: int = 3, timeout: int = 3):
    for attempt in range(1, max_retries + 1):
        try:
            requests.get("https://google.com", timeout=timeout)
            return True
        except Exception as e:
            logger.warning(f"Network check failed (attempt {attempt}/{max_retries}): {e}")
            if attempt < max_retries:
                time.sleep(1)
    logger.warning("Network unreachable after retries. Skipping this cycle.")
    return False

# --- Database Setup ---
try:
    db_url = f"postgresql://{DB_USER}:{DB_PASS_ENCODED}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    engine = db.create_engine(db_url, connect_args={'connect_timeout': 5})
    Session = sessionmaker(bind=engine)
    session = Session()
    logger.info("Database connection successful.")
except Exception as e:
    logger.error(f"Database connection failed: {e}")
    sys.exit(1)

# --- Create/Check Quotes Table ---
def setup_quotes_table():
    try:
        metadata = db.MetaData()
        quotes_table = db.Table(
            'quotes', metadata,
            db.Column('token', db.String(50)),
            db.Column('tsym', db.String(50)),
            db.Column('timestamp_added', db.DateTime(timezone=True)),
            db.Column('open', db.Numeric),
            db.Column('high', db.Numeric),
            db.Column('low', db.Numeric),
            db.Column('close', db.Numeric),
            db.Column('ltp', db.Numeric),
            db.Column('ltq', db.Numeric),
            db.Column('ltt', db.Text),
            db.Column('volume', db.Numeric),
            db.Column('atp', db.Numeric),
            db.Column('oi', db.Numeric),
            db.Column('totalbuyqty', db.Numeric),
            db.Column('totalsellqty', db.Numeric),
            db.Column('top5_total_bid_qty', db.Numeric),
            db.Column('top5_total_ask_qty', db.Numeric),
            db.PrimaryKeyConstraint('token', 'timestamp_added', name='quotes_pk'),
            extend_existing=True
        )
        metadata.create_all(engine, checkfirst=True)

        with engine.connect() as conn:
            # Check for primary key
            result = conn.execute(db.text(
                "SELECT constraint_name FROM information_schema.table_constraints "
                "WHERE table_name = 'quotes' AND constraint_type = 'PRIMARY KEY'"
            )).fetchone()
            if not result:
                logger.info("Adding primary key constraint to quotes table...")
                conn.execute(db.text("ALTER TABLE quotes ADD CONSTRAINT quotes_pk PRIMARY KEY (token, timestamp_added)"))
                conn.commit()

            # Check for index
            result = conn.execute(db.text(
                "SELECT indexname FROM pg_indexes WHERE tablename = 'quotes' AND indexname = 'idx_quotes_token_timestamp'"
            )).fetchone()
            if not result:
                logger.info("Creating index idx_quotes_token_timestamp...")
                conn.execute(db.text("CREATE INDEX IF NOT EXISTS idx_quotes_token_timestamp ON quotes (token, timestamp_added)"))
                conn.commit()
            else:
                logger.info("Index idx_quotes_token_timestamp already exists.")

        logger.info("Quotes table setup complete.")
    except Exception as e:
        logger.error(f"Failed to setup quotes table: {e}\n{traceback.format_exc()}")
        sys.exit(1)

setup_quotes_table()

# --- Shoonya API Setup ---
try:
    loader = ShoonyaSessionLoader()
    if not loader.load():
        logger.error("Shoonya API initialization failed.")
        sys.exit(1)
    api = loader.api
    logger.info("Shoonya API initialized successfully.")
except Exception as e:
    logger.error(f"Shoonya API initialization failed: {e}\n{traceback.format_exc()}")
    sys.exit(1)

# --- Helper Functions ---
def is_market_hours():
    now_ist = datetime.now(IST)
    is_open = now_ist.weekday() < 5 and MARKET_START_TIME <= now_ist.time() < MARKET_END_TIME
    logger.debug(f"Market hours check: now_ist={now_ist}, time={now_ist.time()}, is_open={is_open}")
    return is_open

def get_next_market_open(now: datetime) -> datetime:
    """Calculate the next market open time for MCX."""
    now = now.astimezone(IST)
    market_open_time = now.replace(hour=MARKET_START_TIME.hour, minute=MARKET_START_TIME.minute, second=MARKET_START_TIME.second, microsecond=0)

    if now.weekday() >= 5:  # Weekend
        days_to_monday = 7 - now.weekday()
        next_open = market_open_time + timedelta(days=days_to_monday)
    elif now.time() < MARKET_START_TIME: # Before market open on a weekday
        next_open = market_open_time
    elif now.time() >= MARKET_END_TIME: # After market close on a weekday
        next_day = now + timedelta(days=1)
        # If tomorrow is Saturday, skip to Monday
        if next_day.weekday() == 5:
            next_open = market_open_time + timedelta(days=3)
        else:
            next_open = market_open_time + timedelta(days=1)
    else: # During market hours
        next_open = now # Should not happen if called correctly

    return next_open.astimezone(IST)

def validate_numeric(value, field_name: str, token: str) -> float:
    try:
        return float(value) if value else 0.0
    except (ValueError, TypeError):
        logger.warning(f"Invalid {field_name} for token {token}: {value}")
        return 0.0

def process_and_store_data(tokens: List[Dict[str, str]], timestamp_now: datetime):
    if not is_network_alive():
        return

    try:
        quotes = fetch_quotes(api, tokens)
        quotes_table = db.Table('quotes', db.MetaData(), autoload_with=engine)
        records_to_insert = []

        for token_info in tokens:
            token = token_info['token']
            tsym = token_info['tsym']
            symbol_data = quotes.get(token, {})
            if not symbol_data or symbol_data.get('stat') != 'Ok':
                logger.warning(f"No valid data for token {token} ({tsym})")
                continue

            logger.debug(f"Raw API response for token {token}: {json.dumps(symbol_data, indent=2)}")

            try:
                top5_bid_qty = sum(validate_numeric(symbol_data.get(f'bq{i}', 0), f'bq{i}', token) for i in range(1, 6))
                top5_ask_qty = sum(validate_numeric(symbol_data.get(f'sq{i}', 0), f'sq{i}', token) for i in range(1, 6))
            except Exception as e:
                logger.error(f"Error calculating top5 quantities for token {token}: {e}")
                top5_bid_qty = top5_ask_qty = 0.0

            ltt = symbol_data.get('ltt', '')
            ltt_str = ''
            if ltt:
                try:
                    ltt_time = datetime.strptime(ltt, '%H:%M:%S').time()
                    ltt_full = datetime.combine(timestamp_now.date(), ltt_time).replace(tzinfo=IST)
                    ltt_str = ltt_full.strftime('%Y-%m-%d %H:%M:%S IST')
                except ValueError as e:
                    logger.warning(f"Invalid ltt format for token {token}: {ltt}, error: {e}")
                    ltt_str = ''

            record = {
                'token': token,
                'tsym': tsym,
                'timestamp_added': timestamp_now.replace(microsecond=0),
                'open': validate_numeric(symbol_data.get('o', 0), 'open', token),
                'high': validate_numeric(symbol_data.get('h', 0), 'high', token),
                'low': validate_numeric(symbol_data.get('l', 0), 'low', token),
                'close': validate_numeric(symbol_data.get('c', 0), 'close', token),
                'ltp': validate_numeric(symbol_data.get('lp', 0), 'ltp', token),
                'ltq': validate_numeric(symbol_data.get('ltq', 0), 'ltq', token),
                'ltt': ltt_str,
                'volume': validate_numeric(symbol_data.get('v', 0), 'volume', token),
                'atp': validate_numeric(symbol_data.get('ap', 0), 'atp', token),
                'oi': validate_numeric(symbol_data.get('oi', 0), 'oi', token),
                'totalbuyqty': validate_numeric(symbol_data.get('tbq', 0), 'totalbuyqty', token),
                'totalsellqty': validate_numeric(symbol_data.get('tsq', 0), 'totalsellqty', token),
                'top5_total_bid_qty': top5_bid_qty,
                'top5_total_ask_qty': top5_ask_qty
            }
            records_to_insert.append(record)

        if records_to_insert:
            try:
                insert_stmt = insert(quotes_table).values(records_to_insert)
                on_conflict_stmt = insert_stmt.on_conflict_do_nothing(index_elements=['token', 'timestamp_added'])
                session.execute(on_conflict_stmt)
                session.commit()
                logger.info(f"Stored {len(records_to_insert)} records at {timestamp_now.strftime('%H:%M:%S')}")
            except Exception as e:
                logger.error(f"Database insert failed: {e}\n{traceback.format_exc()}")
                session.rollback()
        else:
            logger.info("No records to insert.")

    except Exception as e:
        logger.error(f"Error during data processing/storage: {e}\n{traceback.format_exc()}")
        session.rollback()

def wait_for_next_interval(interval: int):
    now = datetime.now(IST)
    wait_seconds = (interval - (now.second % interval)) % interval
    if wait_seconds == 0 and now.microsecond > 100000:
        wait_seconds = interval
    logger.info(f"Waiting {wait_seconds} seconds to align with interval...")
    time.sleep(wait_seconds)

def sleep_until_interruptible(target_time: datetime, check_interval: int = 60):
    """Sleep until target_time, checking for stop key every check_interval seconds."""
    now = datetime.now(IST)
    sleep_duration = (target_time - now).total_seconds()
    if sleep_duration <= 0:
        return
    end_time = time.monotonic() + sleep_duration
    while time.monotonic() < end_time:
        remaining = end_time - time.monotonic()
        sleep_amount = min(remaining, check_interval)
        time.sleep(sleep_amount)
        if msvcrt.kbhit() and msvcrt.getch() in [b'\x1b', b'\x03']:
            raise KeyboardInterrupt

# --- Main Loop ---
def main():
    logger.info("--- Market Data Collector (Shoonya API, MCX) ---")

    if not is_market_hours():
        logger.warning("Market is currently closed.")
        try:
            start_time = time.time()
            user_input = None
            print("Run anyway and wait for next market open? (y/n): ", end='', flush=True)
            while time.time() - start_time < 10:
                if msvcrt.kbhit():
                    char = msvcrt.getch().decode('utf-8').lower()
                    if char in ['y', 'n']:
                        user_input = char
                        print(char)
                        break
                time.sleep(0.1)
            if user_input != 'y':
                logger.info("Exiting.")
                sys.exit(0)
        except:
            user_input = input("Run anyway and wait for next market open? (y/n): ").lower()
            if user_input != 'y':
                logger.info("Exiting.")
                sys.exit(0)

    logger.info("Starting data collection loop. Press Esc or Ctrl+C to stop.")

    try:
        while True:
            now = datetime.now(IST)
            if not is_market_hours():
                next_open = get_next_market_open(now)
                sleep_duration_hours = (next_open - now).total_seconds() / 3600
                logger.info(f"Market closed. Sleeping for {sleep_duration_hours:.1f} hours until {next_open.strftime('%Y-%m-%d %H:%M:%S IST')}.")

                if EXIT_AFTER_MARKET_CLOSE:
                    logger.info("Exiting due to market close (EXIT_AFTER_MARKET_CLOSE=True).")
                    break

                sleep_until_interruptible(next_open)
                continue

            # Align to the next interval before processing
            wait_for_next_interval(FETCH_INTERVAL_SECONDS)

            # Process with an aligned timestamp
            target_timestamp = datetime.now(IST).replace(microsecond=0)
            process_and_store_data(TOKENS, target_timestamp)

    except KeyboardInterrupt:
        logger.info("Keyboard interrupt detected. Shutting down gracefully.")
    finally:
        session.close()
        logger.info("Database session closed. Goodbye.")

if __name__ == "__main__":
    main()