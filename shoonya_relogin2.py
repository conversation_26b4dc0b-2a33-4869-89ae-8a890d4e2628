"""
shoonya_relogin2.py
Minimal module to load Shoonya session token and expose NorenApi instance.
"""

from __future__ import annotations
import json
from typing import Optional
from NorenRestApiPy.NorenApi import NorenApi


class ShoonyaSessionLoader:
    REST_URL = "https://api.shoonya.com/NorenWClientTP/"
    WS_URL   = "wss://api.shoonya.com/NorenWSTP/"

    def __init__(self, cred_file: str = "Cread.json", token_file: str = "session_token.txt"):
        self.cred_file  = cred_file
        self.token_file = token_file
        self._api       = None
        self._creds     = None

    @property
    def api(self) -> Optional[NorenApi]:
        """Returns the ready-to-use NorenApi instance after session load."""
        return self._api

    def load(self) -> bool:
        """Load credentials and session token, initialize NorenApi."""
        try:
            with open(self.cred_file, "r") as fp:
                self._creds = json.load(fp)
            with open(self.token_file, "r") as fp:
                token = fp.read().strip()
        except Exception as e:
            print(f"[ShoonyaSessionLoader] Error loading files: {e}")
            return False

        class _Api(NorenApi):
            def __init__(self_):
                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)

        self._api = _Api()
        self._api.set_session(self._creds["user_id"], self._creds["password"], token)
        print("[ShoonyaSessionLoader] Session loaded successfully.")
        return True


# ----------------------------- #
# Example usage
# ----------------------------- #
if __name__ == "__main__":
    loader = ShoonyaSessionLoader()
    if loader.load():
        api = loader.api
        # Now you can use `api` for any Shoonya call, e.g.:
        # api.place_order(...), api.get_order_book(), etc.