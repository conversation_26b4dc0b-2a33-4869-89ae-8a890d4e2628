"""
Minimal, fast loader for Shoonya session with NorenApi instance.
"""

import json
from NorenRestApiPy.NorenApi import NorenApi

REST_URL = "https://api.shoonya.com/NorenWClientTP/"
WS_URL   = "wss://api.shoonya.com/NorenWSTP/"

class ShoonyaSessionLoader:
    def __init__(self, cred_file="Cread.json", token_file="session_token.txt"):
        self.cred_file, self.token_file = cred_file, token_file
        self.api = None

    def load(self):
        try:
            with open(self.cred_file) as cf, open(self.token_file) as tf:
                creds, token = json.load(cf), tf.read().strip()
            self.api = NorenApi(host=REST_URL, websocket=WS_URL)
            self.api.set_session(creds["user_id"], creds["password"], token)
            print("[ShoonyaSessionLoader] Session loaded.")
            return True
        except Exception as e:
            print(f"[ShoonyaSessionLoader] Error: {e}")
            return False

# Example usage
if __name__ == "__main__":
    loader = ShoonyaSessionLoader()
    if loader.load():
        api = loader.api
        # Use api e.g. api.get_order_book()
