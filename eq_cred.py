# eq_cred.py
import json
from urllib.parse import quote_plus

# Load credentials from Cread.json
with open("Cread.json", "r") as f:
    creds = json.load(f)

USER_ID = creds["user_id"]
PASSWORD = creds["password"]
VENDOR_CODE = creds["vendor_code"]
IMEI = creds["imei"]
APP_KEY = creds["app_key"]
TOTP = creds["totp"]

# Database configuration
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "equity_DB"
DB_USER = "postgres"
DB_PASS = "Muni@555"
DB_PASS_ENCODED = quote_plus(DB_PASS)  # Muni%40555