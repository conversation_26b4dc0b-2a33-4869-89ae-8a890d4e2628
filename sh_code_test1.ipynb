{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c0e23670", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ShoonyaSessionLoader] Session loaded successfully.\n"]}], "source": ["\"\"\"\n", "shoonya_relogin2.py\n", "Minimal module to load Shoonya session token and expose NorenApi instance.\n", "\"\"\"\n", "\n", "from __future__ import annotations\n", "import json\n", "from typing import Optional\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "\n", "class ShoonyaSessionLoader:\n", "    REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "    WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "    def __init__(self, cred_file: str = \"Cread.json\", token_file: str = \"session_token.txt\"):\n", "        self.cred_file  = cred_file\n", "        self.token_file = token_file\n", "        self._api       = None\n", "        self._creds     = None\n", "\n", "    @property\n", "    def api(self) -> Optional[NorenApi]:\n", "        \"\"\"Returns the ready-to-use NorenApi instance after session load.\"\"\"\n", "        return self._api\n", "\n", "    def load(self) -> bool:\n", "        \"\"\"Load credentials and session token, initialize NorenApi.\"\"\"\n", "        try:\n", "            with open(self.cred_file, \"r\") as fp:\n", "                self._creds = json.load(fp)\n", "            with open(self.token_file, \"r\") as fp:\n", "                token = fp.read().strip()\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error loading files: {e}\")\n", "            return False\n", "\n", "        class _Api(NorenApi):\n", "            def __init__(self_):\n", "                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)\n", "\n", "        self._api = _Api()\n", "        self._api.set_session(self._creds[\"user_id\"], self._creds[\"password\"], token)\n", "        print(\"[ShoonyaSessionLoader] Session loaded successfully.\")\n", "        return True\n", "\n", "\n", "# ----------------------------- #\n", "# Example usage\n", "# ----------------------------- #\n", "if __name__ == \"__main__\":\n", "    loader = ShoonyaSessionLoader()\n", "    if loader.load():\n", "        api = loader.api\n", "        # Now you can use `api` for any Shoonya call, e.g.:\n", "        # api.place_order(...), api.get_order_book(), etc."]}, {"cell_type": "code", "execution_count": 3, "id": "91fd8b7f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TCS-EQ token is 11536\n"]}], "source": ["exch  = 'NSE'\n", "query = 'TCS-EQ' # multiple criteria to narrow results \n", "ret = api.searchscrip(exchange=exch, searchtext=query)\n", "\n", "if ret != None:\n", "    symbols = ret['values']\n", "    for symbol in symbols:\n", "        print('{0} token is {1}'.format(symbol['tsym'], symbol['token']))\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "92781f20", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[✓] Saved NSE data to trading_data\\NSE.csv\n", "[✓] Saved BSE data to trading_data\\BSE.csv\n", "[✓] Saved NFO data to trading_data\\NFO.csv\n", "[✓] Saved BFO data to trading_data\\BFO.csv\n", "[✓] Saved MCX data to trading_data\\MCX.csv\n", "[✓] Symbol 'CRUDEOIL20OCT25' found in MCX → Token: 455865\n", "Token: 455865\n"]}], "source": ["# find_tok_symb_sh1.py\n", "import pandas as pd\n", "import requests\n", "import zipfile\n", "import os\n", "from functools import wraps\n", "\n", "def safe_run(func):\n", "    \"\"\"Decorator to catch and log exceptions gracefully.\"\"\"\n", "    @wraps(func)\n", "    def wrapper(*args, **kwargs):\n", "        try:\n", "            return func(*args, **kwargs)\n", "        except Exception as e:\n", "            print(f\"[Error in {func.__name__}]: {e}\")\n", "            return None\n", "    return wrapper\n", "\n", "class TradingData:\n", "    def __init__(self, data_dir=\"trading_data\"):\n", "        self.data_dir = data_dir\n", "        os.makedirs(self.data_dir, exist_ok=True)\n", "        self.urls = {\n", "            \"NSE\": \"https://api.shoonya.com/NSE_symbols.txt.zip\",\n", "            \"BSE\": \"https://api.shoonya.com/BSE_symbols.txt.zip\",\n", "            \"NFO\": \"https://api.shoonya.com/NFO_symbols.txt.zip\",\n", "            \"BFO\": \"https://api.shoonya.com/BFO_symbols.txt.zip\",\n", "            \"MCX\": \"https://api.shoonya.com/MCX_symbols.txt.zip\"\n", "        }\n", "        self.dataframes = {}\n", "\n", "    @safe_run\n", "    def download_and_extract(self):\n", "        \"\"\"Downloads and extracts trading data if not already available.\"\"\"\n", "        for market, url in self.urls.items():\n", "            csv_path = os.path.join(self.data_dir, f\"{market}.csv\")\n", "            if os.path.exists(csv_path):\n", "                print(f\"[✓] {market} CSV already exists.\")\n", "                continue\n", "\n", "            zip_path = os.path.join(self.data_dir, f\"{market}.zip\")\n", "            response = requests.get(url, timeout=10)\n", "            response.raise_for_status()\n", "            with open(zip_path, \"wb\") as f:\n", "                f.write(response.content)\n", "\n", "            with zipfile.ZipFile(zip_path, \"r\") as zip_ref:\n", "                zip_ref.extractall(self.data_dir)\n", "                txt_file = next((f for f in zip_ref.namelist() if f.endswith(\".txt\")), None)\n", "\n", "            if not txt_file:\n", "                print(f\"[✗] No .txt file found in {market} archive.\")\n", "                continue\n", "\n", "            txt_path = os.path.join(self.data_dir, txt_file)\n", "            try:\n", "                df = pd.read_csv(txt_path, delimiter=\",\", encoding=\"utf-8\")\n", "            except Exception:\n", "                df = pd.read_csv(txt_path, delimiter=\"|\", encoding=\"utf-8\")\n", "\n", "            df.to_csv(csv_path, index=False)\n", "            print(f\"[✓] Saved {market} data to {csv_path}\")\n", "\n", "    @safe_run\n", "    def load_data(self):\n", "        \"\"\"Loads CSVs into memory and normalizes column names.\"\"\"\n", "        self.dataframes = {\n", "            market: self._load_csv(os.path.join(self.data_dir, f\"{market}.csv\"))\n", "            for market in self.urls\n", "            if os.path.exists(os.path.join(self.data_dir, f\"{market}.csv\"))\n", "        }\n", "\n", "    def _load_csv(self, path):\n", "        df = pd.read_csv(path)\n", "        df.columns = [col.strip().upper() for col in df.columns]\n", "        df[\"TOKEN\"] = df[\"TOKEN\"].astype(str) if \"TOKEN\" in df.columns else None\n", "        return df\n", "\n", "    def _market_iterator(self):\n", "        \"\"\"Generator to iterate over available markets and DataFrames.\"\"\"\n", "        for market, df in self.dataframes.items():\n", "            yield market, df\n", "\n", "    def search_symbol(self, symbol):\n", "        \"\"\"Returns token for a given trading symbol.\"\"\"\n", "        symbol = symbol.upper()\n", "        for market, df in self._market_iterator():\n", "            symbol_col = next((col for col in [\"TRADINGSYMBOL\", \"SYMBOL\"] if col in df.columns), None)\n", "            if symbol_col and \"TOKEN\" in df.columns:\n", "                match = df[df[symbol_col].str.upper() == symbol]\n", "                if not match.empty:\n", "                    token = match[\"TOKEN\"].values[0]\n", "                    print(f\"[✓] Symbol '{symbol}' found in {market} → Token: {token}\")\n", "                    return token\n", "        print(f\"[✗] Symbol '{symbol}' not found.\")\n", "        return None\n", "\n", "    def search_by_token(self, token):\n", "        \"\"\"Returns trading symbol for a given token.\"\"\"\n", "        token = str(token).strip()\n", "        for market, df in self._market_iterator():\n", "            if \"TOKEN\" in df.columns:\n", "                match = df[df[\"TOKEN\"] == token]\n", "                if not match.empty:\n", "                    symbol_col = next((col for col in [\"TRADINGSYMBOL\", \"SYMBOL\"] if col in match.columns), None)\n", "                    if symbol_col:\n", "                        symbol = match[symbol_col].values[0]\n", "                        print(f\"[✓] Token '{token}' found in {market} → Symbol: {symbol}\")\n", "                        return symbol\n", "        print(f\"[✗] Token '{token}' not found.\")\n", "        return None\n", "    \n", "# ------------------ Usage ------------------ #\n", "if __name__ == \"__main__\":\n", "    trading_data = TradingData()\n", "    trading_data.download_and_extract()  # Download and extract data (or use local CSVs if available)\n", "    trading_data.load_data()             # Load CSV files into memory\n", "\n", "    user_symbol = input(\"Enter the trading symbol (e.g., CRUDEOIL18JUN25): \").strip()\n", "    token = trading_data.search_symbol(user_symbol)\n", "    print(f\"Token: {token}\")"]}, {"cell_type": "code", "execution_count": 13, "id": "e1e0f43e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'stat': 'Ok',\n", " 'values': [{'exch': 'NSE',\n", "   'token': '19585',\n", "   'tsym': 'BSE-EQ',\n", "   'cname': 'BSE LIMITED',\n", "   'instname': 'EQ',\n", "   'symname': 'BSE',\n", "   'seg': 'EQT',\n", "   'pp': '2',\n", "   'ls': '1',\n", "   'ti': '0.10'}]}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["exch  = 'NSE'\n", "\n", "ret = api.searchscrip(exchange='NSE', searchtext='BSE-EQ')\n", "ret"]}, {"cell_type": "code", "execution_count": 2, "id": "296b2757", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'request_time': '12:54:07 03-10-2025',\n", " 'stat': 'Ok',\n", " 'exch': 'NSE',\n", " 'tsym': 'KAKATCEM-EQ',\n", " 'cname': 'KAKATIYA CEM SUGAR &IND L',\n", " 'symname': 'KAKATCEM',\n", " 'seg': 'EQT',\n", " 'instname': 'EQ',\n", " 'isin': 'INE437B01014',\n", " 'pp': '2',\n", " 'ls': '1',\n", " 'ti': '0.01',\n", " 'mult': '1',\n", " 'lut': '1759476228',\n", " 'uc': '178.58',\n", " 'lc': '119.05',\n", " 'wk52_h': '226.88',\n", " 'wk52_l': '130.30',\n", " 'issuecap': '7773858.000000',\n", " 'cutof_all': 'false',\n", " 'prcftr_d': '(1 / 1 ) * (1 / 1)',\n", " 'token': '1811',\n", " 'lp': '148.00',\n", " 'c': '148.82',\n", " 'ord_msg': 'Security is under - EPS in the scrip is zero/Less than 100 unique PAN traded in previous 30 days would you like to continue?',\n", " 'h': '150.95',\n", " 'l': '147.44',\n", " 'ap': '149.02',\n", " 'o': '148.90',\n", " 'v': '311',\n", " 'ltq': '4',\n", " 'ltt': '12:53:12',\n", " 'ltd': '03-10-2025',\n", " 'tbq': '8870',\n", " 'tsq': '5117',\n", " 'bp1': '147.75',\n", " 'sp1': '149.40',\n", " 'bp2': '147.70',\n", " 'sp2': '149.50',\n", " 'bp3': '147.25',\n", " 'sp3': '151.40',\n", " 'bp4': '147.10',\n", " 'sp4': '151.49',\n", " 'bp5': '147.01',\n", " 'sp5': '151.50',\n", " 'bq1': '90',\n", " 'sq1': '10',\n", " 'bq2': '5',\n", " 'sq2': '90',\n", " 'bq3': '1',\n", " 'sq3': '30',\n", " 'bq4': '1',\n", " 'sq4': '13',\n", " 'bq5': '47',\n", " 'sq5': '590',\n", " 'bo1': '1',\n", " 'so1': '1',\n", " 'bo2': '1',\n", " 'so2': '1',\n", " 'bo3': '1',\n", " 'so3': '1',\n", " 'bo4': '1',\n", " 'so4': '1',\n", " 'bo5': '1',\n", " 'so5': '1'}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["exch  = 'NSE'\n", "token = '1811'\n", "ret = api.get_quotes(exchange=exch, token=token)\n", "ret"]}, {"cell_type": "code", "execution_count": 4, "id": "fa411d51", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[✓] NSE CSV already exists.\n", "[✓] BSE CSV already exists.\n", "[✓] NFO CSV already exists.\n", "[✓] BFO CSV already exists.\n", "[✓] MCX CSV already exists.\n", "[✓] Symbol 'CRUDEOIL20OCT25' found in MCX → Token: 455865\n", "[ShoonyaSessionLoader] Session loaded successfully.\n", "\n", "Processed Quote Data:\n", "{\n", "    \"request_time\": \"23:00:11 29-09-2025\",\n", "    \"stat\": \"Ok\",\n", "    \"exch\": \"MCX\",\n", "    \"tsym\": \"CRUDEOIL20OCT25\",\n", "    \"symname\": \"CRUDEOIL\",\n", "    \"seg\": \"COM\",\n", "    \"exd\": \"20-OCT-2025\",\n", "    \"instname\": \"FUTCOM\",\n", "    \"optt\": \"XX\",\n", "    \"pp\": \"2\",\n", "    \"ls\": \"100\",\n", "    \"ti\": \"1.00\",\n", "    \"mult\": \"1\",\n", "    \"lut\": \"1759167004\",\n", "    \"uc\": \"6066.00\",\n", "    \"lc\": \"5600.00\",\n", "    \"scrip_base_prc\": \"5194.00\",\n", "    \"wk52_h\": \"6053.00\",\n", "    \"wk52_l\": \"5375.00\",\n", "    \"oi\": \"10872\",\n", "    \"cutof_all\": \"false\",\n", "    \"strprc\": \"0.00\",\n", "    \"prcftr_d\": \"(1 / 1 ) * (1 / 1)\",\n", "    \"token\": \"455865\",\n", "    \"lp\": \"5615.00\",\n", "    \"c\": \"5833.00\",\n", "    \"h\": \"5816.00\",\n", "    \"l\": \"5612.00\",\n", "    \"ap\": \"5704.26\",\n", "    \"o\": \"5800.00\",\n", "    \"v\": \"17093\",\n", "    \"ltq\": \"1\",\n", "    \"ltt\": \"23:00:04\",\n", "    \"ltd\": \"29-09-2025\",\n", "    \"tbq\": \"329\",\n", "    \"tsq\": \"939\",\n", "    \"bp1\": \"5615.00\",\n", "    \"sp1\": \"5617.00\",\n", "    \"bp2\": \"5614.00\",\n", "    \"sp2\": \"5618.00\",\n", "    \"bp3\": \"5613.00\",\n", "    \"sp3\": \"5619.00\",\n", "    \"bp4\": \"5612.00\",\n", "    \"sp4\": \"5620.00\",\n", "    \"bp5\": \"5611.00\",\n", "    \"sp5\": \"5621.00\",\n", "    \"bq1\": \"32\",\n", "    \"sq1\": \"8\",\n", "    \"bq2\": \"18\",\n", "    \"sq2\": \"18\",\n", "    \"bq3\": \"17\",\n", "    \"sq3\": \"29\",\n", "    \"bq4\": \"55\",\n", "    \"sq4\": \"8\",\n", "    \"bq5\": \"8\",\n", "    \"sq5\": \"8\",\n", "    \"bo1\": \"10\",\n", "    \"so1\": \"5\",\n", "    \"bo2\": \"6\",\n", "    \"so2\": \"7\",\n", "    \"bo3\": \"7\",\n", "    \"so3\": \"11\",\n", "    \"bo4\": \"8\",\n", "    \"so4\": \"3\",\n", "    \"bo5\": \"6\",\n", "    \"so5\": \"5\",\n", "    \"t5bq\": \"130\",\n", "    \"t5sq\": \"71\",\n", "    \"und_exch\": \"MCX\",\n", "    \"und_tk\": \"294\"\n", "}\n"]}], "source": ["from __future__ import annotations\n", "import json\n", "import pandas as pd\n", "import requests\n", "import zipfile\n", "import os\n", "from typing import Optional\n", "from functools import wraps\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# ShoonyaSessionLoader class (from shoonya_relogin2.py)\n", "class ShoonyaSessionLoader:\n", "    REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "    WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "    def __init__(self, cred_file: str = \"Cread.json\", token_file: str = \"session_token.txt\"):\n", "        self.cred_file  = cred_file\n", "        self.token_file = token_file\n", "        self._api       = None\n", "        self._creds     = None\n", "\n", "    @property\n", "    def api(self) -> Optional[NorenApi]:\n", "        \"\"\"Returns the ready-to-use NorenApi instance after session load.\"\"\"\n", "        return self._api\n", "\n", "    def load(self) -> bool:\n", "        \"\"\"Load credentials and session token, initialize NorenApi.\"\"\"\n", "        try:\n", "            with open(self.cred_file, \"r\") as fp:\n", "                self._creds = json.load(fp)\n", "            with open(self.token_file, \"r\") as fp:\n", "                token = fp.read().strip()\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error loading files: {e}\")\n", "            return False\n", "\n", "        class _Api(NorenApi):\n", "            def __init__(self_):\n", "                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)\n", "\n", "        self._api = _Api()\n", "        self._api.set_session(self._creds[\"user_id\"], self._creds[\"password\"], token)\n", "        print(\"[ShoonyaSessionLoader] Session loaded successfully.\")\n", "        return True\n", "\n", "# TradingData class (from find_tok_symb_sh1.py)\n", "class TradingData:\n", "    def __init__(self, data_dir=\"trading_data\"):\n", "        self.data_dir = data_dir\n", "        os.makedirs(self.data_dir, exist_ok=True)\n", "        self.urls = {\n", "            \"NSE\": \"https://api.shoonya.com/NSE_symbols.txt.zip\",\n", "            \"BSE\": \"https://api.shoonya.com/BSE_symbols.txt.zip\",\n", "            \"NFO\": \"https://api.shoonya.com/NFO_symbols.txt.zip\",\n", "            \"BFO\": \"https://api.shoonya.com/BFO_symbols.txt.zip\",\n", "            \"MCX\": \"https://api.shoonya.com/MCX_symbols.txt.zip\"\n", "        }\n", "        self.dataframes = {}\n", "\n", "    @staticmethod\n", "    def safe_run(func):\n", "        \"\"\"Decorator to catch and log exceptions gracefully.\"\"\"\n", "        @wraps(func)\n", "        def wrapper(*args, **kwargs):\n", "            try:\n", "                return func(*args, **kwargs)\n", "            except Exception as e:\n", "                print(f\"[Error in {func.__name__}]: {e}\")\n", "                return None\n", "        return wrapper\n", "\n", "    @safe_run\n", "    def download_and_extract(self):\n", "        \"\"\"Downloads and extracts trading data if not already available.\"\"\"\n", "        for market, url in self.urls.items():\n", "            csv_path = os.path.join(self.data_dir, f\"{market}.csv\")\n", "            if os.path.exists(csv_path):\n", "                print(f\"[✓] {market} CSV already exists.\")\n", "                continue\n", "\n", "            zip_path = os.path.join(self.data_dir, f\"{market}.zip\")\n", "            response = requests.get(url, timeout=10)\n", "            response.raise_for_status()\n", "            with open(zip_path, \"wb\") as f:\n", "                f.write(response.content)\n", "\n", "            with zipfile.ZipFile(zip_path, \"r\") as zip_ref:\n", "                zip_ref.extractall(self.data_dir)\n", "                txt_file = next((f for f in zip_ref.namelist() if f.endswith(\".txt\")), None)\n", "\n", "            if not txt_file:\n", "                print(f\"[✗] No .txt file found in {market} archive.\")\n", "                continue\n", "\n", "            txt_path = os.path.join(self.data_dir, txt_file)\n", "            try:\n", "                df = pd.read_csv(txt_path, delimiter=\",\", encoding=\"utf-8\")\n", "            except Exception:\n", "                df = pd.read_csv(txt_path, delimiter=\"|\", encoding=\"utf-8\")\n", "\n", "            df.to_csv(csv_path, index=False)\n", "            print(f\"[✓] Saved {market} data to {csv_path}\")\n", "\n", "    @safe_run\n", "    def load_data(self):\n", "        \"\"\"Loads CSVs into memory and normalizes column names.\"\"\"\n", "        self.dataframes = {\n", "            market: self._load_csv(os.path.join(self.data_dir, f\"{market}.csv\"))\n", "            for market in self.urls\n", "            if os.path.exists(os.path.join(self.data_dir, f\"{market}.csv\"))\n", "        }\n", "\n", "    def _load_csv(self, path):\n", "        df = pd.read_csv(path)\n", "        df.columns = [col.strip().upper() for col in df.columns]\n", "        df[\"TOKEN\"] = df[\"TOKEN\"].astype(str) if \"TOKEN\" in df.columns else None\n", "        return df\n", "\n", "    def _market_iterator(self):\n", "        \"\"\"Generator to iterate over available markets and DataFrames.\"\"\"\n", "        for market, df in self.dataframes.items():\n", "            yield market, df\n", "\n", "    def search_symbol(self, symbol):\n", "        \"\"\"Returns token for a given trading symbol.\"\"\"\n", "        symbol = symbol.upper()\n", "        for market, df in self._market_iterator():\n", "            symbol_col = next((col for col in [\"TRADINGSYMBOL\", \"SYMBOL\"] if col in df.columns), None)\n", "            if symbol_col and \"TOKEN\" in df.columns:\n", "                match = df[df[symbol_col].str.upper() == symbol]\n", "                if not match.empty:\n", "                    token = match[\"TOKEN\"].values[0]\n", "                    print(f\"[✓] Symbol '{symbol}' found in {market} → Token: {token}\")\n", "                    return token, market\n", "        print(f\"[✗] Symbol '{symbol}' not found.\")\n", "        return None, None\n", "\n", "def process_quotes(quote_data: dict) -> dict:\n", "    \"\"\"\n", "    Process the quote data to add total buy and sell quantities for the first 5 levels.\n", "    Ensures 't5bq' and 't5sq' appear before 'und_exch' and 'und_tk' in the output.\n", "    \n", "    Args:\n", "        quote_data (dict): The raw quote data from api.get_quotes()\n", "        \n", "    Returns:\n", "        dict: Modified quote data with 't5bq' and 't5sq' added at the correct position\n", "    \"\"\"\n", "    try:\n", "        # Calculate total buy quantity (t5bq) from bq1 to bq5\n", "        buy_quantities = [int(quote_data.get(f'bq{i}', 0)) for i in range(1, 6)]\n", "        t5bq = sum(buy_quantities)\n", "        \n", "        # Calculate total sell quantity (t5sq) from sq1 to sq5\n", "        sell_quantities = [int(quote_data.get(f'sq{i}', 0)) for i in range(1, 6)]\n", "        t5sq = sum(sell_quantities)\n", "        \n", "        # Create a new dictionary to control key order\n", "        modified_quote = {}\n", "        # Copy all keys up to 'und_exch'\n", "        for key in quote_data:\n", "            if key == 'und_exch':\n", "                break\n", "            modified_quote[key] = quote_data[key]\n", "        \n", "        # Insert t5bq and t5sq before und_exch and und_tk\n", "        modified_quote['t5bq'] = str(t5bq)\n", "        modified_quote['t5sq'] = str(t5sq)\n", "        \n", "        # Add the remaining keys (und_exch and und_tk)\n", "        modified_quote['und_exch'] = quote_data.get('und_exch', '')\n", "        modified_quote['und_tk'] = quote_data.get('und_tk', '')\n", "        \n", "        return modified_quote\n", "    except Exception as e:\n", "        print(f\"[Error in process_quotes]: {e}\")\n", "        return quote_data\n", "\n", "# Main execution\n", "if __name__ == \"__main__\":\n", "    # Step 1: Load trading data and find token\n", "    trading_data = TradingData()\n", "    trading_data.download_and_extract()  # Download and extract data\n", "    trading_data.load_data()             # Load CSV files into memory\n", "\n", "    user_symbol = input(\"Enter the trading symbol (e.g., CRUDEOIL20OCT25): \").strip()\n", "    token, exchange = trading_data.search_symbol(user_symbol)\n", "    \n", "    if token and exchange:\n", "        # Step 2: Initialize API session\n", "        loader = ShoonyaSessionLoader()\n", "        if loader.load():\n", "            api = loader.api\n", "            # Step 3: Fetch and process quotes\n", "            try:\n", "                quote_data = api.get_quotes(exchange=exchange, token=token)\n", "                modified_quote = process_quotes(quote_data)\n", "                print(\"\\nProcessed Quote Data:\")\n", "                print(json.dumps(modified_quote, indent=4))\n", "            except Exception as e:\n", "                print(f\"[Error fetching quotes]: {e}\")\n", "        else:\n", "            print(\"[✗] Failed to initialize API session.\")\n", "    else:\n", "        print(\"[✗] Cannot proceed without valid token and exchange.\")"]}, {"cell_type": "code", "execution_count": 21, "id": "9ed5fcb0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[✓] NSE CSV already exists.\n", "[✓] BSE CSV already exists.\n", "[✓] NFO CSV already exists.\n", "[✓] BFO CSV already exists.\n", "[✓] MCX CSV already exists.\n", "[✓] Symbol 'NIFTY30SEP25C24600' found in NFO → Token: 60453\n", "[ShoonyaSessionLoader] Session loaded successfully.\n", "\n", "Processed Quote Data:\n", "{\n", "    \"request_time\": \"05:06:56 30-09-2025\",\n", "    \"stat\": \"Ok\",\n", "    \"exch\": \"NFO\",\n", "    \"tsym\": \"NIFTY30SEP25C24600\",\n", "    \"cname\": \"NIFTY 30SEP25 24600 CE \",\n", "    \"symname\": \"NIFTY\",\n", "    \"seg\": \"DER\",\n", "    \"exd\": \"30-SEP-2025\",\n", "    \"instname\": \"OPTIDX\",\n", "    \"optt\": \"CE\",\n", "    \"pp\": \"2\",\n", "    \"ls\": \"75\",\n", "    \"ti\": \"0.05\",\n", "    \"mult\": \"1\",\n", "    \"lut\": \"17:00:02 29-09-2025\",\n", "    \"uc\": \"456.60\",\n", "    \"lc\": \"0.05\",\n", "    \"oi\": \"3165900\",\n", "    \"cutof_all\": \"false\",\n", "    \"strprc\": \"24600.00\",\n", "    \"prcftr_d\": \"(1 / 1 ) * (1 / 1)\",\n", "    \"token\": \"60453\",\n", "    \"lp\": \"116.00\",\n", "    \"c\": \"144.35\",\n", "    \"h\": \"256.15\",\n", "    \"l\": \"96.75\",\n", "    \"ap\": \"148.62\",\n", "    \"o\": \"145.50\",\n", "    \"v\": \"145773000\",\n", "    \"ltq\": \"150\",\n", "    \"ltt\": \"20:59:59\",\n", "    \"ltd\": \"29-09-2025\",\n", "    \"tbq\": \"630375\",\n", "    \"tsq\": \"242550\",\n", "    \"bp1\": \"115.65\",\n", "    \"sp1\": \"116.00\",\n", "    \"bp2\": \"115.60\",\n", "    \"sp2\": \"116.05\",\n", "    \"bp3\": \"115.50\",\n", "    \"sp3\": \"116.55\",\n", "    \"bp4\": \"115.40\",\n", "    \"sp4\": \"116.60\",\n", "    \"bp5\": \"115.30\",\n", "    \"sp5\": \"116.95\",\n", "    \"bq1\": \"750\",\n", "    \"sq1\": \"1050\",\n", "    \"bq2\": \"300\",\n", "    \"sq2\": \"600\",\n", "    \"bq3\": \"450\",\n", "    \"sq3\": \"225\",\n", "    \"bq4\": \"300\",\n", "    \"sq4\": \"450\",\n", "    \"bq5\": \"300\",\n", "    \"sq5\": \"825\",\n", "    \"bo1\": \"2\",\n", "    \"so1\": \"5\",\n", "    \"bo2\": \"1\",\n", "    \"so2\": \"2\",\n", "    \"bo3\": \"2\",\n", "    \"so3\": \"1\",\n", "    \"bo4\": \"1\",\n", "    \"so4\": \"1\",\n", "    \"bo5\": \"1\",\n", "    \"so5\": \"2\",\n", "    \"t5bq\": \"2100\",\n", "    \"t5sq\": \"3150\",\n", "    \"und_exch\": \"NSE\",\n", "    \"und_tk\": \"26000\"\n", "}\n"]}], "source": ["from __future__ import annotations\n", "import json\n", "import pandas as pd\n", "import requests\n", "import zipfile\n", "import os\n", "from typing import Optional\n", "from functools import wraps\n", "from datetime import datetime\n", "import pytz\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# ShoonyaSessionLoader class\n", "class ShoonyaSessionLoader:\n", "    REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "    WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "    def __init__(self, cred_file: str = \"Cread.json\", token_file: str = \"session_token.txt\"):\n", "        self.cred_file  = cred_file\n", "        self.token_file = token_file\n", "        self._api       = None\n", "        self._creds     = None\n", "\n", "    @property\n", "    def api(self) -> Optional[NorenApi]:\n", "        \"\"\"Returns the ready-to-use NorenApi instance after session load.\"\"\"\n", "        return self._api\n", "\n", "    def load(self) -> bool:\n", "        \"\"\"Load credentials and session token, initialize NorenApi.\"\"\"\n", "        try:\n", "            with open(self.cred_file, \"r\") as fp:\n", "                self._creds = json.load(fp)\n", "            with open(self.token_file, \"r\") as fp:\n", "                token = fp.read().strip()\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error loading files: {e}\")\n", "            return False\n", "\n", "        class _Api(NorenApi):\n", "            def __init__(self_):\n", "                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)\n", "\n", "        self._api = _Api()\n", "        self._api.set_session(self._creds[\"user_id\"], self._creds[\"password\"], token)\n", "        print(\"[ShoonyaSessionLoader] Session loaded successfully.\")\n", "        return True\n", "\n", "# TradingData class\n", "class TradingData:\n", "    def __init__(self, data_dir=\"trading_data\"):\n", "        self.data_dir = data_dir\n", "        os.makedirs(self.data_dir, exist_ok=True)\n", "        self.urls = {\n", "            \"NSE\": \"https://api.shoonya.com/NSE_symbols.txt.zip\",\n", "            \"BSE\": \"https://api.shoonya.com/BSE_symbols.txt.zip\",\n", "            \"NFO\": \"https://api.shoonya.com/NFO_symbols.txt.zip\",\n", "            \"BFO\": \"https://api.shoonya.com/BFO_symbols.txt.zip\",\n", "            \"MCX\": \"https://api.shoonya.com/MCX_symbols.txt.zip\"\n", "        }\n", "        self.dataframes = {}\n", "\n", "    @staticmethod\n", "    def safe_run(func):\n", "        \"\"\"Decorator to catch and log exceptions gracefully.\"\"\"\n", "        @wraps(func)\n", "        def wrapper(*args, **kwargs):\n", "            try:\n", "                return func(*args, **kwargs)\n", "            except Exception as e:\n", "                print(f\"[Error in {func.__name__}]: {e}\")\n", "                return None\n", "        return wrapper\n", "\n", "    @safe_run\n", "    def download_and_extract(self):\n", "        \"\"\"Downloads and extracts trading data if not already available.\"\"\"\n", "        for market, url in self.urls.items():\n", "            csv_path = os.path.join(self.data_dir, f\"{market}.csv\")\n", "            if os.path.exists(csv_path):\n", "                print(f\"[✓] {market} CSV already exists.\")\n", "                continue\n", "\n", "            zip_path = os.path.join(self.data_dir, f\"{market}.zip\")\n", "            response = requests.get(url, timeout=10)\n", "            response.raise_for_status()\n", "            with open(zip_path, \"wb\") as f:\n", "                f.write(response.content)\n", "\n", "            with zipfile.ZipFile(zip_path, \"r\") as zip_ref:\n", "                zip_ref.extractall(self.data_dir)\n", "                txt_file = next((f for f in zip_ref.namelist() if f.endswith(\".txt\")), None)\n", "\n", "            if not txt_file:\n", "                print(f\"[✗] No .txt file found in {market} archive.\")\n", "                continue\n", "\n", "            txt_path = os.path.join(self.data_dir, txt_file)\n", "            try:\n", "                df = pd.read_csv(txt_path, delimiter=\",\", encoding=\"utf-8\")\n", "            except Exception:\n", "                df = pd.read_csv(txt_path, delimiter=\"|\", encoding=\"utf-8\")\n", "\n", "            df.to_csv(csv_path, index=False)\n", "            print(f\"[✓] Saved {market} data to {csv_path}\")\n", "\n", "    @safe_run\n", "    def load_data(self):\n", "        \"\"\"Loads CSVs into memory and normalizes column names.\"\"\"\n", "        self.dataframes = {\n", "            market: self._load_csv(os.path.join(self.data_dir, f\"{market}.csv\"))\n", "            for market in self.urls\n", "            if os.path.exists(os.path.join(self.data_dir, f\"{market}.csv\"))\n", "        }\n", "\n", "    def _load_csv(self, path):\n", "        df = pd.read_csv(path)\n", "        df.columns = [col.strip().upper() for col in df.columns]\n", "        df[\"TOKEN\"] = df[\"TOKEN\"].astype(str) if \"TOKEN\" in df.columns else None\n", "        return df\n", "\n", "    def _market_iterator(self):\n", "        \"\"\"Generator to iterate over available markets and DataFrames.\"\"\"\n", "        for market, df in self.dataframes.items():\n", "            yield market, df\n", "\n", "    def search_symbol(self, symbol):\n", "        \"\"\"Returns token and exchange for a given trading symbol.\"\"\"\n", "        symbol = symbol.upper()\n", "        for market, df in self._market_iterator():\n", "            symbol_col = next((col for col in [\"TRADINGSYMBOL\", \"SYMBOL\"] if col in df.columns), None)\n", "            if symbol_col and \"TOKEN\" in df.columns:\n", "                match = df[df[symbol_col].str.upper() == symbol]\n", "                if not match.empty:\n", "                    token = match[\"TOKEN\"].values[0]\n", "                    print(f\"[✓] Symbol '{symbol}' found in {market} → Token: {token}\")\n", "                    return token, market\n", "        print(f\"[✗] Symbol '{symbol}' not found.\")\n", "        return None, None\n", "\n", "def process_quotes(quote_data: dict) -> dict:\n", "    \"\"\"\n", "    Process the quote data to add total buy and sell quantities for the first 5 levels\n", "    and convert timestamps (request_time, ltt, ltd, lut) to IST. Ensures 't5bq' and 't5sq'\n", "    appear before 'und_exch' and 'und_tk'.\n", "    \n", "    Args:\n", "        quote_data (dict): The raw quote data from api.get_quotes()\n", "        \n", "    Returns:\n", "        dict: Modified quote data with 't5bq', 't5sq', and IST-converted timestamps\n", "    \"\"\"\n", "    try:\n", "        # Calculate total buy quantity (t5bq) from bq1 to bq5\n", "        buy_quantities = [int(quote_data.get(f'bq{i}', 0)) for i in range(1, 6)]\n", "        t5bq = sum(buy_quantities)\n", "        \n", "        # Calculate total sell quantity (t5sq) from sq1 to sq5\n", "        sell_quantities = [int(quote_data.get(f'sq{i}', 0)) for i in range(1, 6)]\n", "        t5sq = sum(sell_quantities)\n", "        \n", "        # Convert timestamps to IST\n", "        ist = pytz.timezone('Asia/Kolkata')\n", "        \n", "        # Process request_time (e.g., '21:33:17 29-09-2025')\n", "        request_time = quote_data.get('request_time', '')\n", "        if request_time:\n", "            try:\n", "                dt = datetime.strptime(request_time, '%H:%M:%S %d-%m-%Y')\n", "                dt = pytz.utc.localize(dt).astimezone(ist)\n", "                request_time = dt.strftime('%H:%M:%S %d-%m-%Y')\n", "            except ValueError as e:\n", "                print(f\"[Error parsing request_time]: {e}\")\n", "\n", "        # Process ltt and ltd (e.g., ltt='21:33:00', ltd='29-09-2025')\n", "        ltt = quote_data.get('ltt', '')\n", "        ltd = quote_data.get('ltd', '')\n", "        if ltt and ltd:\n", "            try:\n", "                dt_str = f\"{ltt} {ltd}\"\n", "                dt = datetime.strptime(dt_str, '%H:%M:%S %d-%m-%Y')\n", "                dt = pytz.utc.localize(dt).astimezone(ist)\n", "                ltt = dt.strftime('%H:%M:%S')\n", "                ltd = dt.strftime('%d-%m-%Y')\n", "            except ValueError as e:\n", "                print(f\"[Error parsing ltt/ltd]: {e}\")\n", "\n", "        # Process lut (Unix timestamp, e.g., '1759167416')\n", "        lut = quote_data.get('lut', '')\n", "        if lut:\n", "            try:\n", "                dt = datetime.fromtimestamp(int(lut), tz=pytz.utc)\n", "                dt = dt.astimezone(ist)\n", "                lut = dt.strftime('%H:%M:%S %d-%m-%Y')\n", "            except ValueError as e:\n", "                print(f\"[Error parsing lut]: {e}\")\n", "\n", "        # Create a new dictionary to control key order\n", "        modified_quote = {}\n", "        # Copy all keys up to 'und_exch'\n", "        for key in quote_data:\n", "            if key == 'und_exch':\n", "                break\n", "            if key == 'request_time':\n", "                modified_quote[key] = request_time\n", "            elif key == 'ltt':\n", "                modified_quote[key] = ltt\n", "            elif key == 'ltd':\n", "                modified_quote[key] = ltd\n", "            elif key == 'lut':\n", "                modified_quote[key] = lut\n", "            else:\n", "                modified_quote[key] = quote_data[key]\n", "        \n", "        # Insert t5bq and t5sq before und_exch and und_tk\n", "        modified_quote['t5bq'] = str(t5bq)\n", "        modified_quote['t5sq'] = str(t5sq)\n", "        \n", "        # Add the remaining keys (und_exch and und_tk)\n", "        modified_quote['und_exch'] = quote_data.get('und_exch', '')\n", "        modified_quote['und_tk'] = quote_data.get('und_tk', '')\n", "        \n", "        return modified_quote\n", "    except Exception as e:\n", "        print(f\"[Error in process_quotes]: {e}\")\n", "        return quote_data\n", "\n", "# Main execution\n", "if __name__ == \"__main__\":\n", "    # Step 1: Load trading data and find token\n", "    trading_data = TradingData()\n", "    trading_data.download_and_extract()  # Download and extract data\n", "    trading_data.load_data()             # Load CSV files into memory\n", "\n", "    user_symbol = input(\"Enter the trading symbol (e.g., CRUDEOIL20OCT25): \").strip()\n", "    token, exchange = trading_data.search_symbol(user_symbol)\n", "    \n", "    if token and exchange:\n", "        # Step 2: Initialize API session\n", "        loader = ShoonyaSessionLoader()\n", "        if loader.load():\n", "            api = loader.api\n", "            # Step 3: Fetch and process quotes\n", "            try:\n", "                quote_data = api.get_quotes(exchange=exchange, token=token)\n", "                modified_quote = process_quotes(quote_data)\n", "                print(\"\\nProcessed Quote Data:\")\n", "                print(json.dumps(modified_quote, indent=4))\n", "            except Exception as e:\n", "                print(f\"[Error fetching quotes]: {e}\")\n", "        else:\n", "            print(\"[✗] Failed to initialize API session.\")\n", "    else:\n", "        print(\"[✗] Cannot proceed without valid token and exchange.\")"]}, {"cell_type": "code", "execution_count": null, "id": "9307b9f2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[✓] NSE CSV already exists.\n", "[✓] BSE CSV already exists.\n", "[✓] NFO CSV already exists.\n", "[✓] BFO CSV already exists.\n", "[✓] MCX CSV already exists.\n", "[✓] Symbol 'CRUDEOIL20OCT25' found in MCX → Token: 455865\n", "[ShoonyaSessionLoader] Session loaded successfully.\n", "[Error parsing ltt]: time data '23:23:59' does not match format '%Y-%m-%d %H:%M:%S'\n", "\n", "Processed Quote Data:\n", "{\n", "    \"totalbuyqty\": \"346\",\n", "    \"totalsellqty\": \"911\",\n", "    \"o\": \"5800.00\",\n", "    \"h\": \"5816.00\",\n", "    \"l\": \"5605.00\",\n", "    \"c\": \"5833.00\",\n", "    \"ltq\": \"2\",\n", "    \"ltt\": \"23:23:59\",\n", "    \"ltp\": \"5611.00\",\n", "    \"v\": \"18148\",\n", "    \"atp\": \"5698.80\",\n", "    \"oi\": \"10614\",\n", "    \"pdoi\": \"0\",\n", "    \"top5_total_bid_qty\": \"161\",\n", "    \"top5_total_ask_qty\": \"109\"\n", "}\n"]}], "source": ["from __future__ import annotations\n", "import json\n", "import pandas as pd\n", "import requests\n", "import zipfile\n", "import os\n", "from typing import Optional\n", "from functools import wraps\n", "from datetime import datetime\n", "import pytz\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# ShoonyaSessionLoader class\n", "class ShoonyaSessionLoader:\n", "    REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "    WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "    def __init__(self, cred_file: str = \"Cread.json\", token_file: str = \"session_token.txt\"):\n", "        self.cred_file  = cred_file\n", "        self.token_file = token_file\n", "        self._api       = None\n", "        self._creds     = None\n", "\n", "    @property\n", "    def api(self) -> Optional[NorenApi]:\n", "        \"\"\"Returns the ready-to-use NorenApi instance after session load.\"\"\"\n", "        return self._api\n", "\n", "    def load(self) -> bool:\n", "        \"\"\"Load credentials and session token, initialize NorenApi.\"\"\"\n", "        try:\n", "            with open(self.cred_file, \"r\") as fp:\n", "                self._creds = json.load(fp)\n", "            with open(self.token_file, \"r\") as fp:\n", "                token = fp.read().strip()\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error loading files: {e}\")\n", "            return False\n", "\n", "        class _Api(NorenApi):\n", "            def __init__(self_):\n", "                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)\n", "\n", "        self._api = _Api()\n", "        self._api.set_session(self._creds[\"user_id\"], self._creds[\"password\"], token)\n", "        print(\"[ShoonyaSessionLoader] Session loaded successfully.\")\n", "        return True\n", "\n", "# TradingData class\n", "class TradingData:\n", "    def __init__(self, data_dir=\"trading_data\"):\n", "        self.data_dir = data_dir\n", "        os.makedirs(self.data_dir, exist_ok=True)\n", "        self.urls = {\n", "            \"NSE\": \"https://api.shoonya.com/NSE_symbols.txt.zip\",\n", "            \"BSE\": \"https://api.shoonya.com/BSE_symbols.txt.zip\",\n", "            \"NFO\": \"https://api.shoonya.com/NFO_symbols.txt.zip\",\n", "            \"BFO\": \"https://api.shoonya.com/BFO_symbols.txt.zip\",\n", "            \"MCX\": \"https://api.shoonya.com/MCX_symbols.txt.zip\"\n", "        }\n", "        self.dataframes = {}\n", "\n", "    @staticmethod\n", "    def safe_run(func):\n", "        \"\"\"Decorator to catch and log exceptions gracefully.\"\"\"\n", "        @wraps(func)\n", "        def wrapper(*args, **kwargs):\n", "            try:\n", "                return func(*args, **kwargs)\n", "            except Exception as e:\n", "                print(f\"[Error in {func.__name__}]: {e}\")\n", "                return None\n", "        return wrapper\n", "\n", "    @safe_run\n", "    def download_and_extract(self):\n", "        \"\"\"Downloads and extracts trading data if not already available.\"\"\"\n", "        for market, url in self.urls.items():\n", "            csv_path = os.path.join(self.data_dir, f\"{market}.csv\")\n", "            if os.path.exists(csv_path):\n", "                print(f\"[✓] {market} CSV already exists.\")\n", "                continue\n", "\n", "            zip_path = os.path.join(self.data_dir, f\"{market}.zip\")\n", "            response = requests.get(url, timeout=10)\n", "            response.raise_for_status()\n", "            with open(zip_path, \"wb\") as f:\n", "                f.write(response.content)\n", "\n", "            with zipfile.ZipFile(zip_path, \"r\") as zip_ref:\n", "                zip_ref.extractall(self.data_dir)\n", "                txt_file = next((f for f in zip_ref.namelist() if f.endswith(\".txt\")), None)\n", "\n", "            if not txt_file:\n", "                print(f\"[✗] No .txt file found in {market} archive.\")\n", "                continue\n", "\n", "            txt_path = os.path.join(self.data_dir, txt_file)\n", "            try:\n", "                df = pd.read_csv(txt_path, delimiter=\",\", encoding=\"utf-8\")\n", "            except Exception:\n", "                df = pd.read_csv(txt_path, delimiter=\"|\", encoding=\"utf-8\")\n", "\n", "            df.to_csv(csv_path, index=False)\n", "            print(f\"[✓] Saved {market} data to {csv_path}\")\n", "\n", "    @safe_run\n", "    def load_data(self):\n", "        \"\"\"Loads CSVs into memory and normalizes column names.\"\"\"\n", "        self.dataframes = {\n", "            market: self._load_csv(os.path.join(self.data_dir, f\"{market}.csv\"))\n", "            for market in self.urls\n", "            if os.path.exists(os.path.join(self.data_dir, f\"{market}.csv\"))\n", "        }\n", "\n", "    def _load_csv(self, path):\n", "        df = pd.read_csv(path)\n", "        df.columns = [col.strip().upper() for col in df.columns]\n", "        df[\"TOKEN\"] = df[\"TOKEN\"].astype(str) if \"TOKEN\" in df.columns else None\n", "        return df\n", "\n", "    def _market_iterator(self):\n", "        \"\"\"Generator to iterate over available markets and DataFrames.\"\"\"\n", "        for market, df in self.dataframes.items():\n", "            yield market, df\n", "\n", "    def search_symbol(self, symbol):\n", "        \"\"\"Returns token and exchange for a given trading symbol.\"\"\"\n", "        symbol = symbol.upper()\n", "        for market, df in self._market_iterator():\n", "            symbol_col = next((col for col in [\"TRADINGSYMBOL\", \"SYMBOL\"] if col in df.columns), None)\n", "            if symbol_col and \"TOKEN\" in df.columns:\n", "                match = df[df[symbol_col].str.upper() == symbol]\n", "                if not match.empty:\n", "                    token = match[\"TOKEN\"].values[0]\n", "                    print(f\"[✓] Symbol '{symbol}' found in {market} → Token: {token}\")\n", "                    return token, market\n", "        print(f\"[✗] Symbol '{symbol}' not found.\")\n", "        return None, None\n", "\n", "def process_quotes(quote_data: dict) -> dict:\n", "    \"\"\"\n", "    Process the quote data to extract specific keys, calculate top5_total_bid_qty and\n", "    top5_total_ask_qty, and ensure ltt is in IST. Returns only the requested keys for database storage.\n", "    \n", "    Args:\n", "        quote_data (dict): The raw quote data from api.get_quotes()\n", "        \n", "    Returns:\n", "        dict: Filtered quote data with only the specified keys\n", "    \"\"\"\n", "    try:\n", "        # Define the keys to include in the output\n", "        output_keys = [\n", "            \"totalbuyqty\", \"totalsellqty\", \"o\", \"h\", \"l\", \"c\", \"ltq\", \"ltt\", \"ltp\",\n", "            \"v\", \"atp\", \"oi\", \"top5_total_bid_qty\", \"top5_total_ask_qty\"\n", "        ]\n", "        \n", "        # Calculate top5_total_bid_qty (sum of bq1 to bq5) and top5_total_ask_qty (sum of sq1 to sq5)\n", "        buy_quantities = [int(quote_data.get(f'bq{i}', 0)) for i in range(1, 6)]\n", "        top5_total_bid_qty = sum(buy_quantities)\n", "        \n", "        sell_quantities = [int(quote_data.get(f'sq{i}', 0)) for i in range(1, 6)]\n", "        top5_total_ask_qty = sum(sell_quantities)\n", "        \n", "        # Process ltt timestamp (assumed to be in IST, e.g., '2025-09-29 17:36:50 IST')\n", "        ltt = quote_data.get('ltt', '')\n", "        if ltt:\n", "            try:\n", "                # Remove 'IST' suffix if present and parse\n", "                ltt_clean = ltt.replace(\" IST\", \"\").strip()\n", "                dt = datetime.strptime(ltt_clean, '%Y-%m-%d %H:%M:%S')\n", "                # Ensure IST timezone\n", "                ist = pytz.timezone('Asia/Kolkata')\n", "                dt = ist.localize(dt)\n", "                ltt = dt.strftime('%Y-%m-%d %H:%M:%S IST')\n", "            except ValueError as e:\n", "                print(f\"[Error parsing ltt]: {e}\")\n", "\n", "        # Create output dictionary with only the specified keys\n", "        result = {\n", "            \"totalbuyqty\": quote_data.get('tbq', '0'),\n", "            \"totalsellqty\": quote_data.get('tsq', '0'),\n", "            \"o\": quote_data.get('o', '0'),\n", "            \"h\": quote_data.get('h', '0'),\n", "            \"l\": quote_data.get('l', '0'),\n", "            \"c\": quote_data.get('c', '0'),\n", "            \"ltq\": quote_data.get('ltq', '0'),\n", "            \"ltt\": ltt,\n", "            \"ltp\": quote_data.get('lp', '0'),\n", "            \"v\": quote_data.get('v', '0'),\n", "            \"atp\": quote_data.get('ap', '0'),\n", "            \"oi\": quote_data.get('oi', '0'),\n", "            \"top5_total_bid_qty\": str(top5_total_bid_qty),\n", "            \"top5_total_ask_qty\": str(top5_total_ask_qty)\n", "        }\n", "        \n", "        return result\n", "    except Exception as e:\n", "        print(f\"[Error in process_quotes]: {e}\")\n", "        return {}\n", "\n", "# Main execution\n", "if __name__ == \"__main__\":\n", "    # Hardcoded symbol\n", "    SYMBOL = \"CRUDEOIL20OCT25\"\n", "\n", "    # Step 1: Load trading data and find token\n", "    trading_data = TradingData()\n", "    trading_data.download_and_extract()  # Download and extract data\n", "    trading_data.load_data()             # Load CSV files into memory\n", "\n", "    token, exchange = trading_data.search_symbol(SYMBOL)\n", "    \n", "    if token and exchange:\n", "        # Step 2: Initialize API session\n", "        loader = ShoonyaSessionLoader()\n", "        if loader.load():\n", "            api = loader.api\n", "            # Step 3: Fetch and process quotes\n", "            try:\n", "                quote_data = api.get_quotes(exchange=exchange, token=token)\n", "                modified_quote = process_quotes(quote_data)\n", "                print(\"\\nProcessed Quote Data:\")\n", "                print(json.dumps(modified_quote, indent=4))\n", "            except Exception as e:\n", "                print(f\"[Error fetching quotes]: {e}\")\n", "        else:\n", "            print(\"[✗] Failed to initialize API session.\")\n", "    else:\n", "        print(f\"[✗] Cannot proceed without valid token and exchange for symbol {SYMBOL}.\")"]}, {"cell_type": "code", "execution_count": 3, "id": "f48249fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[✓] NSE CSV already exists.\n", "[✓] BSE CSV already exists.\n", "[✓] NFO CSV already exists.\n", "[✓] BFO CSV already exists.\n", "[✓] MCX CSV already exists.\n", "[✓] Cached lookup for TCS-EQ: Token=11536, Exchange=NSE\n", "[ShoonyaSessionLoader] Session loaded successfully.\n", "\n", "Processed Quote Data:\n", "{\n", "    \"totalbuyqty\": \"177416\",\n", "    \"totalsellqty\": \"128324\",\n", "    \"o\": \"2911.00\",\n", "    \"h\": \"2915.30\",\n", "    \"l\": \"2896.10\",\n", "    \"c\": \"2896.10\",\n", "    \"ltq\": \"1\",\n", "    \"ltt\": \"13:10:20\",\n", "    \"ltp\": \"2908.30\",\n", "    \"v\": \"690748\",\n", "    \"atp\": \"2904.55\",\n", "    \"oi\": \"0\",\n", "    \"top5_total_bid_qty\": \"138\",\n", "    \"top5_total_ask_qty\": \"872\"\n", "}\n", "\n", "[Performance] Total execution time: 5.481s\n"]}], "source": ["from __future__ import annotations\n", "import json\n", "import pandas as pd\n", "import requests\n", "import zipfile\n", "import os\n", "from typing import Optional, Dict, Tuple\n", "from functools import wraps\n", "from datetime import datetime\n", "import pytz\n", "import numpy as np  # For blazing-fast array sums\n", "import time  # For benchmarking and rate limiting\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# ShoonyaSessionLoader class (unchanged, but loaded once)\n", "class ShoonyaSessionLoader:\n", "    REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "    WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "    def __init__(self, cred_file: str = \"Cread.json\", token_file: str = \"session_token.txt\"):\n", "        self.cred_file  = cred_file\n", "        self.token_file = token_file\n", "        self._api       = None\n", "        self._creds     = None\n", "\n", "    @property\n", "    def api(self) -> Optional[NorenApi]:\n", "        \"\"\"Returns the ready-to-use NorenApi instance after session load.\"\"\"\n", "        return self._api\n", "\n", "    def load(self) -> bool:\n", "        \"\"\"Load credentials and session token, initialize NorenApi.\"\"\"\n", "        try:\n", "            with open(self.cred_file, \"r\") as fp:\n", "                self._creds = json.load(fp)\n", "            with open(self.token_file, \"r\") as fp:\n", "                token = fp.read().strip()\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error loading files: {e}\")\n", "            return False\n", "\n", "        class _Api(NorenApi):\n", "            def __init__(self_):\n", "                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)\n", "\n", "        self._api = _Api()\n", "        self._api.set_session(self._creds[\"user_id\"], self._creds[\"password\"], token)\n", "        print(\"[ShoonyaSessionLoader] Session loaded successfully.\")\n", "        return True\n", "\n", "# Optimized TradingData class: Download once, cache tokens globally for instant lookup\n", "class TradingData:\n", "    _token_cache: Dict[str, Tuple[str, str]] = {}  # Global cache: symbol -> (token, exchange)\n", "\n", "    def __init__(self, data_dir=\"trading_data\"):\n", "        self.data_dir = data_dir\n", "        os.makedirs(self.data_dir, exist_ok=True)\n", "        self.urls = {\n", "            \"NSE\": \"https://api.shoonya.com/NSE_symbols.txt.zip\",\n", "            \"BSE\": \"https://api.shoonya.com/BSE_symbols.txt.zip\",\n", "            \"NFO\": \"https://api.shoonya.com/NFO_symbols.txt.zip\",\n", "            \"BFO\": \"https://api.shoonya.com/BFO_symbols.txt.zip\",\n", "            \"MCX\": \"https://api.shoonya.com/MCX_symbols.txt.zip\"\n", "        }\n", "        self.dataframes = {}\n", "\n", "    @staticmethod\n", "    def safe_run(func):\n", "        \"\"\"Decorator to catch and log exceptions gracefully.\"\"\"\n", "        @wraps(func)\n", "        def wrapper(*args, **kwargs):\n", "            try:\n", "                return func(*args, **kwargs)\n", "            except Exception as e:\n", "                print(f\"[Error in {func.__name__}]: {e}\")\n", "                return None\n", "        return wrapper\n", "\n", "    @safe_run\n", "    def download_and_extract(self):\n", "        \"\"\"Downloads and extracts trading data if not already available (one-time).\"\"\"\n", "        for market, url in self.urls.items():\n", "            csv_path = os.path.join(self.data_dir, f\"{market}.csv\")\n", "            if os.path.exists(csv_path):\n", "                print(f\"[✓] {market} CSV already exists.\")\n", "                continue\n", "\n", "            zip_path = os.path.join(self.data_dir, f\"{market}.zip\")\n", "            response = requests.get(url, timeout=10)\n", "            response.raise_for_status()\n", "            with open(zip_path, \"wb\") as f:\n", "                f.write(response.content)\n", "\n", "            with zipfile.ZipFile(zip_path, \"r\") as zip_ref:\n", "                zip_ref.extractall(self.data_dir)\n", "                txt_file = next((f for f in zip_ref.namelist() if f.endswith(\".txt\")), None)\n", "\n", "            if not txt_file:\n", "                print(f\"[✗] No .txt file found in {market} archive.\")\n", "                continue\n", "\n", "            txt_path = os.path.join(self.data_dir, txt_file)\n", "            try:\n", "                df = pd.read_csv(txt_path, delimiter=\",\", encoding=\"utf-8\")\n", "            except Exception:\n", "                df = pd.read_csv(txt_path, delimiter=\"|\", encoding=\"utf-8\")\n", "\n", "            df.to_csv(csv_path, index=False)\n", "            print(f\"[✓] Saved {market} data to {csv_path}\")\n", "\n", "    @safe_run\n", "    def load_data(self):\n", "        \"\"\"Loads CSVs into memory and populates global cache (one-time, fast with pandas).\"\"\"\n", "        if self._token_cache:  # Already cached\n", "            return\n", "        self.dataframes = {\n", "            market: self._load_csv(os.path.join(self.data_dir, f\"{market}.csv\"))\n", "            for market in self.urls\n", "            if os.path.exists(os.path.join(self.data_dir, f\"{market}.csv\"))\n", "        }\n", "        # Build cache: O(n) once, then O(1) lookups\n", "        for market, df in self.dataframes.items():\n", "            if \"TRADINGSYMBOL\" in df.columns and \"TOKEN\" in df.columns:\n", "                for _, row in df.iterrows():\n", "                    sym = str(row[\"TRADINGSYMBOL\"]).upper()\n", "                    self._token_cache[sym] = (str(row[\"TOKEN\"]), market)\n", "            elif \"SYMBOL\" in df.columns and \"TOKEN\" in df.columns:\n", "                for _, row in df.iterrows():\n", "                    sym = str(row[\"SYMBOL\"]).upper()\n", "                    self._token_cache[sym] = (str(row[\"TOKEN\"]), market)\n", "\n", "    def _load_csv(self, path):\n", "        df = pd.read_csv(path)  # Pandas is optimized for this\n", "        df.columns = [col.strip().upper() for col in df.columns]\n", "        df[\"TOKEN\"] = df[\"TOKEN\"].astype(str) if \"TOKEN\" in df.columns else None\n", "        return df\n", "\n", "    @classmethod\n", "    def get_token(cls, symbol: str) -> Optional[Tuple[str, str]]:\n", "        \"\"\"O(1) cached lookup.\"\"\"\n", "        return cls._token_cache.get(symbol.upper())\n", "\n", "def process_quotes(quote_data: dict) -> dict:\n", "    \"\"\"\n", "    Blazing-fast processing with NumPy vectorization.\n", "    Extracts specific keys, calculates top5 sums, handles ltt (IST).\n", "    \"\"\"\n", "    try:\n", "        # NumPy for C-like speed on sums (vectorized, no loops)\n", "        buy_qtys = np.array([int(quote_data.get(f'bq{i}', 0)) for i in range(1, 6)])\n", "        sell_qtys = np.array([int(quote_data.get(f'sq{i}', 0)) for i in range(1, 6)])\n", "        top5_total_bid_qty = int(np.sum(buy_qtys))\n", "        top5_total_ask_qty = int(np.sum(sell_qtys))\n", "\n", "        # Fast ltt processing (minimal overhead)\n", "        ltt = quote_data.get('ltt', '')\n", "        if ltt:\n", "            try:\n", "                ltt_clean = ltt.replace(\" IST\", \"\").strip()\n", "                dt = datetime.strptime(ltt_clean, '%Y-%m-%d %H:%M:%S')\n", "                ist = pytz.timezone('Asia/Kolkata')\n", "                dt = ist.localize(dt)\n", "                ltt = dt.strftime('%Y-%m-%d %H:%M:%S IST')\n", "            except ValueError:\n", "                pass  # Keep original if invalid\n", "\n", "        # Direct dict (no loops, fast)\n", "        result = {\n", "            \"totalbuyqty\": quote_data.get('tbq', '0'),\n", "            \"totalsellqty\": quote_data.get('tsq', '0'),\n", "            \"o\": quote_data.get('o', '0'),\n", "            \"h\": quote_data.get('h', '0'),\n", "            \"l\": quote_data.get('l', '0'),\n", "            \"c\": quote_data.get('c', '0'),\n", "            \"ltq\": quote_data.get('ltq', '0'),\n", "            \"ltt\": ltt,\n", "            \"ltp\": quote_data.get('lp', '0'),\n", "            \"v\": quote_data.get('v', '0'),\n", "            \"atp\": quote_data.get('ap', '0'),\n", "            \"oi\": quote_data.get('oi', '0'),\n", "            \"top5_total_bid_qty\": str(top5_total_bid_qty),\n", "            \"top5_total_ask_qty\": str(top5_total_ask_qty)\n", "        }\n", "        return result\n", "    except Exception as e:\n", "        print(f\"[Error in process_quotes]: {e}\")\n", "        return {}\n", "\n", "# Main execution: Optimized for speed\n", "if __name__ == \"__main__\":\n", "    start_time = time.time()  # Benchmark\n", "\n", "    # Hardcoded symbol\n", "    SYMBOL = \"TCS-EQ\"\n", "\n", "    # Step 1: One-time data load and cache (fast after first run)\n", "    trading_data = TradingData()\n", "    trading_data.download_and_extract()\n", "    trading_data.load_data()\n", "\n", "    token, exchange = TradingData.get_token(SYMBOL)\n", "    \n", "    if token and exchange:\n", "        print(f\"[✓] Cached lookup for {SYMBOL}: Token={token}, Exchange={exchange}\")\n", "        # Step 2: API session (loaded once)\n", "        loader = ShoonyaSessionLoader()\n", "        if loader.load():\n", "            api = loader.api\n", "            # Step 3: Fetch and process (network-bound, but processing is instant)\n", "            try:\n", "                quote_data = api.get_quotes(exchange=exchange, token=token)\n", "                modified_quote = process_quotes(quote_data)\n", "                print(\"\\nProcessed Quote Data:\")\n", "                print(json.dumps(modified_quote, indent=4))\n", "                \n", "                # Optional: Rate limit for multiple calls (e.g., in a loop)\n", "                # time.sleep(0.5)  # Safe delay to stay under limits\n", "            except Exception as e:\n", "                print(f\"[Error fetching quotes]: {e}\")\n", "        else:\n", "            print(\"[✗] Failed to initialize API session.\")\n", "    else:\n", "        print(f\"[✗] Symbol {SYMBOL} not found in cache.\")\n", "\n", "    end_time = time.time()\n", "    print(f\"\\n[Performance] Total execution time: {end_time - start_time:.3f}s\")"]}, {"cell_type": "code", "execution_count": 6, "id": "f65f860f", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'stat': 'Ok',\n", " 'values': [{'exch': 'NSE',\n", "   'token': '26017',\n", "   'tsym': 'INDIAVIX',\n", "   'nontrd': '1',\n", "   'cname': 'INDIA VIX',\n", "   'instname': 'UNDIND',\n", "   'symname': 'INDIAVIX',\n", "   'seg': 'EQT',\n", "   'pp': '2',\n", "   'ls': '1',\n", "   'ti': '0.05'}]}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["api.searchscrip(exchange='NSE', searchtext='INDIAVIX')"]}, {"cell_type": "code", "execution_count": 8, "id": "1a0ef7fe", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'request_time': '14:26:45 30-09-2025',\n", " 'stat': 'Ok',\n", " 'exch': 'NSE',\n", " 'tsym': 'INDIAVIX',\n", " 'cname': 'INDIA VIX',\n", " 'symname': 'INDIAVIX',\n", " 'seg': 'EQT',\n", " 'instname': 'UNDIND',\n", " 'pp': '2',\n", " 'prcftr': '1.000000',\n", " 'ls': '1',\n", " 'ti': '0.05',\n", " 'mult': '1',\n", " 'nontrd': '1',\n", " 'prcftr_d': '(1 / 1 ) * (1 / 1)',\n", " 'token': '26017'}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["exch  = 'NSE'\n", "token = '26017'\n", "ret = api.get_security_info(exchange=exch, token=token)\n", "ret"]}, {"cell_type": "code", "execution_count": 11, "id": "d98def37", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'request_time': '14:31:08 30-09-2025',\n", " 'stat': 'Ok',\n", " 'exch': 'NSE',\n", " 'tsym': 'Nifty 50',\n", " 'cname': 'NIFTY INDEX',\n", " 'symname': 'NIFTY',\n", " 'seg': 'EQT',\n", " 'instname': 'UNDIND',\n", " 'pp': '2',\n", " 'ls': '1',\n", " 'ti': '0.05',\n", " 'mult': '1',\n", " 'lut': '1759222867',\n", " 'wk52_h': '25669.35',\n", " 'wk52_l': '21743.65',\n", " 'toi': '121670650',\n", " 'cutof_all': 'false',\n", " 'prcftr_d': '(1 / 1 ) * (1 / 1)',\n", " 'token': '26000',\n", " 'lp': '24619.80',\n", " 'c': '24634.90',\n", " 'h': '24731.80',\n", " 'l': '24587.70',\n", " 'o': '24691.95'}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["exch  = 'NSE'\n", "# token = '26017'\n", "token = '26000'\n", "ret = api.get_quotes(exchange=exch, token=token)\n", "ret"]}, {"cell_type": "code", "execution_count": 12, "id": "d9c6c4ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[INFO] Token 26000 fetched successfully.\n", "[INFO] Token 26017 fetched successfully.\n", "[INFO] Token 11536 fetched successfully.\n"]}], "source": ["list_tk = ['26000', '26017', '11536']\n", "exch = 'NSE'\n", "\n", "quotes = {}\n", "for token in list_tk:\n", "    try:\n", "        ret = api.get_quotes(exchange=exch, token=token)\n", "        quotes[token] = ret\n", "        print(f\"[INFO] Token {token} fetched successfully.\")\n", "    except Exception as e:\n", "        print(f\"[ERROR] Failed to fetch token {token}: {e}\")"]}, {"cell_type": "code", "execution_count": 13, "id": "7b17fafd", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'26000': {'request_time': '14:34:25 30-09-2025',\n", "  'stat': 'Ok',\n", "  'exch': 'NSE',\n", "  'tsym': 'Nifty 50',\n", "  'cname': 'NIFTY INDEX',\n", "  'symname': 'NIFTY',\n", "  'seg': 'EQT',\n", "  'instname': 'UNDIND',\n", "  'pp': '2',\n", "  'ls': '1',\n", "  'ti': '0.05',\n", "  'mult': '1',\n", "  'lut': '1759223065',\n", "  'wk52_h': '25669.35',\n", "  'wk52_l': '21743.65',\n", "  'toi': '116406475',\n", "  'cutof_all': 'false',\n", "  'prcftr_d': '(1 / 1 ) * (1 / 1)',\n", "  'token': '26000',\n", "  'lp': '24617.75',\n", "  'c': '24634.90',\n", "  'h': '24731.80',\n", "  'l': '24587.70',\n", "  'o': '24691.95'},\n", " '26017': {'request_time': '14:34:26 30-09-2025',\n", "  'stat': 'Ok',\n", "  'exch': 'NSE',\n", "  'tsym': 'INDIAVIX',\n", "  'cname': 'INDIA VIX',\n", "  'symname': 'INDIAVIX',\n", "  'seg': 'EQT',\n", "  'instname': 'UNDIND',\n", "  'pp': '2',\n", "  'ls': '1',\n", "  'ti': '0.05',\n", "  'mult': '1',\n", "  'lut': '1759223061',\n", "  'wk52_h': '23.19',\n", "  'wk52_l': '9.40',\n", "  'cutof_all': 'false',\n", "  'prcftr_d': '(1 / 1 ) * (1 / 1)',\n", "  'token': '26017',\n", "  'lp': '11.54',\n", "  'c': '11.37',\n", "  'h': '11.79',\n", "  'l': '11.00',\n", "  'o': '11.37'},\n", " '11536': {'request_time': '14:34:26 30-09-2025',\n", "  'stat': 'Ok',\n", "  'exch': 'NSE',\n", "  'tsym': 'TCS-EQ',\n", "  'cname': 'TATA CONSULTANCY SERV LT',\n", "  'symname': 'TCS',\n", "  'seg': 'EQT',\n", "  'instname': 'EQ',\n", "  'isin': 'INE467B01029',\n", "  'pp': '2',\n", "  'ls': '1',\n", "  'ti': '0.10',\n", "  'mult': '1',\n", "  'lut': '1759223066',\n", "  'uc': '3185.70',\n", "  'lc': '2606.50',\n", "  'wk52_h': '4494.89',\n", "  'wk52_l': '2891.30',\n", "  'toi': '65760800',\n", "  'issuecap': '3618087518.000000',\n", "  'cutof_all': 'false',\n", "  'prcftr_d': '(1 / 1 ) * (1 / 1)',\n", "  'token': '11536',\n", "  'lp': '2891.80',\n", "  'c': '2896.10',\n", "  'h': '2915.30',\n", "  'l': '2888.00',\n", "  'ap': '2900.22',\n", "  'o': '2911.00',\n", "  'v': '1361462',\n", "  'ltq': '3',\n", "  'ltt': '14:34:25',\n", "  'ltd': '30-09-2025',\n", "  'tbq': '105693',\n", "  'tsq': '133122',\n", "  'bp1': '2891.10',\n", "  'sp1': '2891.80',\n", "  'bp2': '2891.00',\n", "  'sp2': '2891.90',\n", "  'bp3': '2890.90',\n", "  'sp3': '2892.00',\n", "  'bp4': '2890.80',\n", "  'sp4': '2892.10',\n", "  'bp5': '2890.70',\n", "  'sp5': '2892.20',\n", "  'bq1': '1',\n", "  'sq1': '40',\n", "  'bq2': '44',\n", "  'sq2': '17',\n", "  'bq3': '15',\n", "  'sq3': '1226',\n", "  'bq4': '29',\n", "  'sq4': '24',\n", "  'bq5': '70',\n", "  'sq5': '71',\n", "  'bo1': '1',\n", "  'so1': '4',\n", "  'bo2': '9',\n", "  'so2': '2',\n", "  'bo3': '1',\n", "  'so3': '8',\n", "  'bo4': '4',\n", "  'so4': '1',\n", "  'bo5': '6',\n", "  'so5': '4'}}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["quotes"]}, {"cell_type": "code", "execution_count": 15, "id": "d51a5b64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ShoonyaSessionLoader] Session loaded successfully.\n", "[INFO] Token 26000 fetched.\n", "[INFO] Token 26017 fetched.\n", "[INFO] Token 11536 fetched.\n", "\n", "--- Final Quote Dump ---\n", "26000: {'request_time': '14:53:05 30-09-2025', 'stat': 'Ok', 'exch': 'NSE', 'tsym': 'Nifty 50', 'cname': 'NIFTY INDEX', 'symname': 'NIFTY', 'seg': 'EQT', 'instname': 'UNDIND', 'pp': '2', 'ls': '1', 'ti': '0.05', 'mult': '1', 'lut': '1759224185', 'wk52_h': '25669.35', 'wk52_l': '21743.65', 'toi': '92984050', 'cutof_all': 'false', 'prcftr_d': '(1 / 1 ) * (1 / 1)', 'token': '26000', 'lp': '24626.30', 'c': '24634.90', 'h': '24731.80', 'l': '24587.70', 'o': '24691.95'}\n", "26017: {'request_time': '14:53:06 30-09-2025', 'stat': 'Ok', 'exch': 'NSE', 'tsym': 'INDIAVIX', 'cname': 'INDIA VIX', 'symname': 'INDIAVIX', 'seg': 'EQT', 'instname': 'UNDIND', 'pp': '2', 'ls': '1', 'ti': '0.05', 'mult': '1', 'lut': '1759224186', 'wk52_h': '23.19', 'wk52_l': '9.40', 'cutof_all': 'false', 'prcftr_d': '(1 / 1 ) * (1 / 1)', 'token': '26017', 'lp': '11.19', 'c': '11.37', 'h': '11.79', 'l': '11.00', 'o': '11.37'}\n", "11536: {'request_time': '14:53:06 30-09-2025', 'stat': 'Ok', 'exch': 'NSE', 'tsym': 'TCS-EQ', 'cname': 'TATA CONSULTANCY SERV LT', 'symname': 'TCS', 'seg': 'EQT', 'instname': 'EQ', 'isin': 'INE467B01029', 'pp': '2', 'ls': '1', 'ti': '0.10', 'mult': '1', 'lut': '1759224186', 'uc': '3185.70', 'lc': '2606.50', 'wk52_h': '4494.89', 'wk52_l': '2891.30', 'toi': '66005625', 'issuecap': '3618087518.000000', 'cutof_all': 'false', 'prcftr_d': '(1 / 1 ) * (1 / 1)', 'token': '11536', 'lp': '2894.40', 'c': '2896.10', 'h': '2915.30', 'l': '2888.00', 'ap': '2899.68', 'o': '2911.00', 'v': '1481336', 'ltq': '1', 'ltt': '14:53:05', 'ltd': '30-09-2025', 'tbq': '111070', 'tsq': '125165', 'bp1': '2894.40', 'sp1': '2894.60', 'bp2': '2894.00', 'sp2': '2894.70', 'bp3': '2893.90', 'sp3': '2894.80', 'bp4': '2893.80', 'sp4': '2894.90', 'bp5': '2893.70', 'sp5': '2895.00', 'bq1': '2', 'sq1': '176', 'bq2': '231', 'sq2': '4', 'bq3': '3', 'sq3': '38', 'bq4': '95', 'sq4': '2', 'bq5': '30', 'sq5': '2', 'bo1': '2', 'so1': '3', 'bo2': '13', 'so2': '2', 'bo3': '1', 'so3': '1', 'bo4': '1', 'so4': '2', 'bo5': '3', 'so5': '1'}\n"]}], "source": ["from __future__ import annotations\n", "import json\n", "import time\n", "from typing import List, Dict\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "class ShoonyaSessionLoader:\n", "    REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "    WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "    def __init__(self, cred_file: str = \"Cread.json\", token_file: str = \"session_token.txt\"):\n", "        self.cred_file  = cred_file\n", "        self.token_file = token_file\n", "        self._api       = None\n", "        self._creds     = None\n", "\n", "    @property\n", "    def api(self) -> <PERSON><PERSON><PERSON><PERSON>:\n", "        return self._api\n", "\n", "    def load(self) -> bool:\n", "        try:\n", "            with open(self.cred_file, \"r\") as fp:\n", "                self._creds = json.load(fp)\n", "            with open(self.token_file, \"r\") as fp:\n", "                token = fp.read().strip()\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error loading files: {e}\")\n", "            return False\n", "\n", "        class _Api(NorenApi):\n", "            def __init__(self_):\n", "                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)\n", "\n", "        self._api = _Api()\n", "        self._api.set_session(self._creds[\"user_id\"], self._creds[\"password\"], token)\n", "        print(\"[ShoonyaSessionLoader] Session loaded successfully.\")\n", "        return True\n", "\n", "def fetch_quotes_batch(api: NorenApi, tokens: List[str], exchange: str = \"NSE\", batch_size: int = 5, delay_sec: float = 0.5) -> Dict[str, dict]:\n", "    \"\"\"\n", "    Fetch quotes for a list of tokens in controlled batches.\n", "    :param api: NorenApi instance\n", "    :param tokens: List of token strings\n", "    :param exchange: Exchange code (default 'NSE')\n", "    :param batch_size: Number of tokens per batch\n", "    :param delay_sec: Delay between batches in seconds\n", "    :return: Dictionary of token -> quote data\n", "    \"\"\"\n", "    quotes = {}\n", "    for i in range(0, len(tokens), batch_size):\n", "        batch = tokens[i:i + batch_size]\n", "        for token in batch:\n", "            try:\n", "                ret = api.get_quotes(exchange=exchange, token=token)\n", "                quotes[token] = ret\n", "                print(f\"[INFO] Token {token} fetched.\")\n", "            except Exception as e:\n", "                print(f\"[ERROR] Token {token} failed: {e}\")\n", "        time.sleep(delay_sec)  # Respect rate limits\n", "    return quotes\n", "\n", "# ----------------------------- #\n", "# Example usage\n", "# ----------------------------- #\n", "if __name__ == \"__main__\":\n", "    loader = ShoonyaSessionLoader()\n", "    if loader.load():\n", "        api = loader.api\n", "        list_tk = ['26000', '26017', '11536']  # Add more tokens as needed\n", "        quotes = fetch_quotes_batch(api, list_tk)\n", "        print(\"\\n--- Final Quote Dump ---\")\n", "        for tk, data in quotes.items():\n", "            print(f\"{tk}: {data}\")"]}, {"cell_type": "code", "execution_count": 2, "id": "43e4030e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ShoonyaSessionLoader] Session loaded successfully.\n", "\n", "[✓] Cached lookup for CRUDEOILM20OCT25: Token=455866, Exchange=MCX\n", "[ShoonyaSessionLoader] Session loaded successfully.\n", "\n", "Processed Quote Data:\n", "{\n", "    \"tsym\": \"CRUDEOILM20OCT25\",\n", "    \"request_time\": \"15:41:09 30-09-2025\",\n", "    \"o\": \"5612.00\",\n", "    \"h\": \"5632.00\",\n", "    \"l\": \"5560.00\",\n", "    \"prev.close\": \"5613.00\",\n", "    \"ltp\": \"5585.00\",\n", "    \"ltq\": \"1\",\n", "    \"ltt\": \"15:41:04\",\n", "    \"v\": \"9169\",\n", "    \"ap\": \"5598.78\",\n", "    \"toi\": \"0\",\n", "    \"oi\": \"11782\",\n", "    \"totalbuyqty\": \"1133\",\n", "    \"totalsellqty\": \"1460\",\n", "    \"top5_total_bid_qty\": \"43\",\n", "    \"top5_total_ask_qty\": \"51\"\n", "}\n"]}], "source": ["from __future__ import annotations\n", "import json\n", "import time\n", "from typing import List, Dict\n", "from NorenRestApiPy.NorenApi import NorenApi\n", "\n", "# ----------------------------- #\n", "# S<PERSON>onya Session Loader\n", "# ----------------------------- #\n", "class ShoonyaSessionLoader:\n", "    REST_URL = \"https://api.shoonya.com/NorenWClientTP/\"\n", "    WS_URL   = \"wss://api.shoonya.com/NorenWSTP/\"\n", "\n", "    def __init__(self, cred_file: str = \"Cread.json\", token_file: str = \"session_token.txt\"):\n", "        self.cred_file  = cred_file\n", "        self.token_file = token_file\n", "        self._api       = None\n", "        self._creds     = None\n", "\n", "    @property\n", "    def api(self) -> <PERSON><PERSON><PERSON><PERSON>:\n", "        return self._api\n", "\n", "    def load(self) -> bool:\n", "        try:\n", "            with open(self.cred_file, \"r\") as fp:\n", "                self._creds = json.load(fp)\n", "            with open(self.token_file, \"r\") as fp:\n", "                token = fp.read().strip()\n", "        except Exception as e:\n", "            print(f\"[ShoonyaSessionLoader] Error loading files: {e}\")\n", "            return False\n", "\n", "        class _Api(NorenApi):\n", "            def __init__(self_):\n", "                super().__init__(host=ShoonyaSessionLoader.REST_URL, websocket=ShoonyaSessionLoader.WS_URL)\n", "\n", "        self._api = _Api()\n", "        self._api.set_session(self._creds[\"user_id\"], self._creds[\"password\"], token)\n", "        print(\"[ShoonyaSessionLoader] Session loaded successfully.\")\n", "        return True\n", "\n", "# ----------------------------- #\n", "# Quote <PERSON> with <PERSON>ll\n", "# ----------------------------- #\n", "def format_quote(token: str, quote: dict) -> None:\n", "    def get(field: str) -> str:\n", "        return quote.get(field, \"0\")\n", "\n", "    tsym = get(\"tsym\")\n", "    print(f\"\\n[✓] Cached lookup for {tsym}: Token={token}, Exchange={get('exch')}\")\n", "    print(\"[ShoonyaSessionLoader] Session loaded successfully.\\n\")\n", "\n", "    processed = {\n", "        \"tsym\": tsym,\n", "        \"request_time\": get(\"request_time\"),\n", "        \"o\": get(\"o\"),\n", "        \"h\": get(\"h\"),\n", "        \"l\": get(\"l\"),\n", "        \"prev.close\": get(\"c\"),\n", "        \"ltp\": get(\"lp\"),\n", "        \"ltq\": get(\"ltq\"),\n", "        \"ltt\": get(\"ltt\"),\n", "        \"v\": get(\"v\"),\n", "        \"ap\": get(\"ap\"),\n", "        \"toi\": get(\"toi\"),\n", "        \"oi\": get(\"oi\"),\n", "        \"totalbuyqty\": get(\"tbq\"),\n", "        \"totalsellqty\": get(\"tsq\"),\n", "        \"top5_total_bid_qty\": str(sum(int(get(f\"bq{i}\")) for i in range(1, 6))),\n", "        \"top5_total_ask_qty\": str(sum(int(get(f\"sq{i}\")) for i in range(1, 6)))\n", "    }\n", "\n", "    print(\"Processed Quote Data:\")\n", "    print(json.dumps(processed, indent=4))\n", "\n", "# ----------------------------- #\n", "# Fast Batch <PERSON>tcher\n", "# ----------------------------- #\n", "def fetch_quotes(api: NorenApi, tokens: List[str], exchange: str = \"MCX\", delay_ms: int = 200) -> None:\n", "    for token in tokens:\n", "        try:\n", "            quote = api.get_quotes(exchange=exchange, token=token)\n", "            format_quote(token, quote)\n", "        except Exception as e:\n", "            print(f\"[ERROR] Token {token} failed: {e}\")\n", "        time.sleep(delay_ms / 1000.0)  # Respect Shoonya rate limit\n", "\n", "# ----------------------------- #\n", "# Main Execution\n", "# ----------------------------- #\n", "if __name__ == \"__main__\":\n", "    loader = ShoonyaSessionLoader()\n", "    if loader.load():\n", "        api = loader.api\n", "        tokens = ['455866']  # Add more tokens as needed\n", "        fetch_quotes(api, tokens)"]}, {"cell_type": "code", "execution_count": 13, "id": "d73732f9", "metadata": {}, "outputs": [], "source": ["exchange = \"MCX\"\n", "tradingsymbol = 'CRUDEOIL20OCT25'\n", "strikeprice = 5600\n", "count = 2\n", "ret = api.get_option_chain(exchange, tradingsymbol, strikeprice, count)\n", "ret"]}, {"cell_type": "code", "execution_count": null, "id": "b0990228", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}