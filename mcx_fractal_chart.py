# mcx_fractal_chart.py
# this code fetches data from database and plots a chart mcx
import dash
from dash import dcc, html, callback_context
from dash.dependencies import Input, Output, State
import plotly.graph_objs as go
import pandas as pd
from sqlalchemy import create_engine
from io import String<PERSON>
from datetime import datetime, time as dt_time
import pytz
import warnings
import json
from urllib.parse import quote_plus

# --- Suppress specific FutureWarning from Plotly/Pandas ---
# This warning is related to a future change in pandas, used internally by plotly.
# It doesn't affect the current functionality, so we can safely ignore it to keep the console clean.
warnings.filterwarnings("ignore", message="The behavior of DatetimeProperties.to_pydatetime is deprecated")
# --- Constants & Configuration ---
IST = pytz.timezone('Asia/Kolkata')

# Define the symbols you want to be available in the dropdown.
# This can be hardcoded or fetched from the database.
SYMBOLS_TO_PROCESS = [
    'CRUDEOILM20OCT25'
]

# --- Load Credentials & Database Connection ---
try:
    with open('Cread.json', 'r') as fp:
        creds = json.load(fp)
    DB_HOST = creds.get('db_host', 'localhost')
    DB_PORT = creds.get('db_port', '5432')
    DB_NAME = creds.get('db_name', 'options_mcx_DB')
    DB_USER = creds.get('db_user', 'postgres')
    DB_PASS = creds.get('db_pass', 'Muni@555')
    DB_PASS_ENCODED = quote_plus(DB_PASS)
    db_url = f"postgresql://{DB_USER}:{DB_PASS_ENCODED}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
except Exception as e:
    print(f"FATAL: Could not read Cread.json or configure database: {e}")
    exit(1)
engine = create_engine(db_url)

# --- Dash App ---
app = dash.Dash(__name__, update_title='Updating...')
app.title = "MCX Fractal Dashboard"

# --- Layout ---
app.layout = html.Div(style={'backgroundColor': '#111111', 'color': '#FFFFFF'}, children=[
    html.H2("📊 MCX Fractal Analysis Chart", style={'textAlign': 'center'}),

    # --- Controls ---
    html.Div([
        html.Label("Select Date:", style={'fontWeight': 'bold'}),
        dcc.DatePickerSingle(
            id='date-picker',
            date=datetime.now(IST).date(),
            display_format='YYYY-MM-DD',
            style={'marginRight': '20px'}
        ),
        html.Label("Select Symbol:", style={'fontWeight': 'bold'}),
        dcc.Dropdown(
            id='symbol-selector',
            options=[{'label': s, 'value': s} for s in SYMBOLS_TO_PROCESS],
            value=SYMBOLS_TO_PROCESS[0],
            clearable=False,
            style={'width': '250px', 'color': '#000'}
        ),
        html.Label("Resampling:", style={'fontWeight': 'bold', 'marginLeft': '20px'}),
        dcc.Dropdown(
            id='resample-selector',
            options=[
                {'label': 'Raw', 'value': 'RAW'},
                {'label': '30s', 'value': '30s'},
                {'label': '1min', 'value': '1min'},
                {'label': '5min', 'value': '5min'},
                {'label': '15min', 'value': '15min'},
                {'label': '1D', 'value': '1D'},
            ],
            value='RAW',
            clearable=False,
            style={'width': '150px', 'color': '#000'}
        ),
    ], style={'display': 'flex', 'gap': '10px', 'justifyContent': 'center', 'alignItems': 'center', 'marginBottom': '10px'}),

    html.Div([
        html.Label("View Mode:", style={'fontWeight': 'bold'}),
        dcc.RadioItems(
            id='view-mode-selector',
            options=[
                {'label': 'Absolute', 'value': 'ABSOLUTE'},
                {'label': 'Normalized', 'value': 'NORMALIZED'},
            ],
            value='ABSOLUTE',
            inline=True,
            style={'marginLeft': '10px'}
        ),
        dcc.Checklist(
            id='toggle-axes',
            options=[{'label': 'Show/Hide All Axes', 'value': 'SHOW'}],
            value=['SHOW'],
            inline=True,
            style={'marginLeft': '20px'}
        )
    ], style={'display': 'flex', 'justifyContent': 'center', 'marginBottom': '20px'}),

    dcc.Store(id='data-store'),
    dcc.Graph(id='analysis-chart', config={'displayModeBar': True, 'scrollZoom': True}),
    dcc.Interval(id='interval-component', interval=5 * 1000, n_intervals=0) # 5-second interval
])

# --- Data Fetching ---
def fetch_data(symbol: str, start_time: datetime = None, end_time: datetime = None):
    """Fetches data for a specific symbol and time range from the 'quotes' table."""
    base_query = """
        SELECT
            timestamp_added, tsym, ltp, atp, ltq,
            totalbuyqty, totalsellqty,
            top5_total_bid_qty, top5_total_ask_qty,
            oi
        FROM quotes
        WHERE tsym = %(symbol)s
    """
    params = {'symbol': symbol}

    if start_time and end_time:
        base_query += " AND timestamp_added BETWEEN %(start)s AND %(end)s"
        params.update({'start': start_time, 'end': end_time})

    query = base_query + " ORDER BY timestamp_added;"

    try:
        df = pd.read_sql(query, engine, params=params, parse_dates=['timestamp_added'])
        if not df.empty:
            df['timestamp_added'] = df['timestamp_added'].dt.tz_convert(IST)
            df = df.ffill().bfill()
        return df
    except Exception as e:
        print(f"Error fetching data: {e}")
        return pd.DataFrame()

def safe_resample(df, rule, agg_rules):
    """Safely resamples a DataFrame, returning an empty one if input is empty."""
    if df.empty:
        return pd.DataFrame()
    return df.set_index('timestamp_added').resample(rule).agg(agg_rules).reset_index()

# --- Chart Callback ---
@app.callback(
    [Output('analysis-chart', 'figure'),
     Output('data-store', 'data')],
    [Input('interval-component', 'n_intervals'),
     Input('date-picker', 'date'),
     Input('symbol-selector', 'value'),
     Input('resample-selector', 'value'),
     Input('toggle-axes', 'value'),
     Input('view-mode-selector', 'value')],
    [State('data-store', 'data')]
)
def update_chart(n, selected_date_str, selected_symbol, resample_rule, axes_visibility, view_mode, stored_data):
    ctx = callback_context
    triggered_id = ctx.triggered[0]['prop_id'].split('.')[0] if ctx.triggered else 'interval-component'

    now_ist = datetime.now(IST)
    selected_date = datetime.strptime(selected_date_str, '%Y-%m-%d').date()
    is_today = selected_date == now_ist.date()
    
    # Define which timeframes should load all historical data
    historical_timeframes = ['5min', '15min', '1D']

    if triggered_id == 'interval-component' and not is_today:
        return dash.no_update, dash.no_update

    # --- Data Loading Logic ---
    # If a historical timeframe is selected, ALWAYS fetch all data, ignoring date/cache.
    if resample_rule in historical_timeframes:
        df = fetch_data(selected_symbol) # Fetch all data
    # Full reload for a new day, symbol, or on first load.
    elif triggered_id in ['symbol-selector', 'date-picker'] or stored_data is None:
        start_time = IST.localize(datetime.combine(selected_date, dt_time.min))
        end_time = now_ist if is_today else IST.localize(datetime.combine(selected_date, dt_time.max))
        df = fetch_data(selected_symbol, start_time, end_time)
    # Incremental update or other changes (view mode, etc.)
    else:
        df = pd.read_json(StringIO(stored_data), orient='split')
        df['timestamp_added'] = pd.to_datetime(df['timestamp_added']).dt.tz_convert(IST)

        # Handle live updates for today's data
        if triggered_id == 'interval-component' and is_today and not df.empty:
            last_timestamp = df['timestamp_added'].max()
            if pd.notna(last_timestamp):
                new_data = fetch_data(selected_symbol, last_timestamp, now_ist)
                if not new_data.empty:
                    df = pd.concat([df, new_data]).drop_duplicates(subset=['tsym', 'timestamp_added'], keep='last')

    if df.empty:
        return go.Figure().update_layout(title_text=f"No data for {selected_symbol} on {selected_date_str}", template='plotly_dark'), dash.no_update

    # Remove rows with NaN in LTP to skip gaps (holidays, no-data periods)
    df = df.dropna(subset=['ltp'])

    stored_df_json = df.to_json(orient='split', date_format='iso')

    # --- Charting Logic ---
    if resample_rule != 'RAW':
        agg_rules = {
            'ltp': 'last', 'atp': 'last', 'oi': 'last',
            'ltq': 'sum', 'totalbuyqty': 'last', 'totalsellqty': 'last',
            'top5_total_bid_qty': 'last', 'top5_total_ask_qty': 'last'
        }
        df = safe_resample(df, resample_rule, agg_rules)
        # Remove NaN rows after resample to connect across gaps
        df = df.dropna(subset=['ltp'])

    if df.empty:
        return go.Figure().update_layout(title_text=f"No data for {selected_symbol} on {selected_date_str}", template='plotly_dark'), stored_df_json

    fig = go.Figure()

    def normalize_series(series):
        min_val, max_val = series.min(), series.max()
        return pd.Series(0.5, index=series.index) if min_val == max_val else (series - min_val) / (max_val - min_val)

    def add_trace(data_df, col, name, yaxis, color, dash='solid', width=1.5):
        if not data_df.empty and col in data_df.columns and data_df[col].notnull().any():
            y_data = normalize_series(data_df[col]) if view_mode == 'NORMALIZED' else data_df[col]
            target_yaxis = 'y1' if view_mode == 'NORMALIZED' else yaxis
            fig.add_trace(go.Scatter(
                x=data_df['timestamp_added'], y=y_data, mode='lines', name=name, yaxis=target_yaxis,
                line=dict(color=color, dash=dash, width=width)
            ))

    # Add traces for the selected symbol
    add_trace(df, 'ltp', 'LTP', 'y1', 'lime', width=2.5)
    add_trace(df, 'atp', 'ATP', 'y1', 'orange', dash='dash')
    add_trace(df, 'oi', 'Open Interest', 'y2', 'gold')
    add_trace(df, 'totalbuyqty', 'Total Buy Qty', 'y3', 'deepskyblue')
    add_trace(df, 'top5_total_bid_qty', 'Top 5 Buy Qty', 'y6', 'mediumspringgreen')
    add_trace(df, 'totalsellqty', 'Total Sell Qty', 'y4', 'tomato')
    add_trace(df, 'top5_total_ask_qty', 'Top 5 Sell Qty', 'y7', 'violet')
    add_trace(df, 'ltq', 'LTQ', 'y5', 'cyan')

    # --- Layout Configuration ---
    fig.update_layout(
        title=f"{selected_symbol} - {resample_rule} View",
        template='plotly_dark',
        height=750,
        xaxis_title='Time (IST)',
        xaxis_rangeslider_visible=False,
        legend=dict(orientation='h', yanchor='bottom', y=1.02, xanchor='right', x=1),
        uirevision=f"{selected_symbol}-{resample_rule}", # Helps preserve zoom
        margin=dict(l=80, r=80, t=100, b=50),
    )

    # For historical views, switch to a category axis to remove time gaps (weekends, etc.)
    if resample_rule in historical_timeframes:
        fig.update_xaxes(type='category')


    if view_mode == 'NORMALIZED':
        fig.update_layout(
            yaxis=dict(title='Normalized Value', showgrid=False, range=[0, 1], visible=True),
            yaxis2=dict(visible=False), yaxis3=dict(visible=False), yaxis4=dict(visible=False),
            yaxis5=dict(visible=False), yaxis6=dict(visible=False), yaxis7=dict(visible=False),
        )
    else: # Absolute View
        show_axes = 'SHOW' in axes_visibility
        axis_config = {
            'y1': {'title': 'Price', 'side': 'left', 'color': 'lime', 'position': 0.0},
            'y2': {'title': 'Open Interest', 'side': 'right', 'color': 'gold', 'position': 1.0, 'overlaying': 'y'},
            'y3': {'title': 'Total Buy', 'side': 'left', 'color': 'deepskyblue', 'position': 0.06, 'overlaying': 'y', 'type': 'log'},
            'y4': {'title': 'Total Sell', 'side': 'right', 'color': 'tomato', 'position': 0.94, 'overlaying': 'y', 'type': 'log'},
            'y5': {'title': 'LTQ', 'side': 'left', 'color': 'cyan', 'position': 0.12, 'overlaying': 'y'},
            'y6': {'title': 'Top 5 Buy', 'side': 'left', 'color': 'mediumspringgreen', 'position': 0.18, 'overlaying': 'y', 'type': 'log'},
            'y7': {'title': 'Top 5 Sell', 'side': 'right', 'color': 'violet', 'position': 0.88, 'overlaying': 'y', 'type': 'log'},
        }

        for axis_id, cfg in axis_config.items():
            layout_key = 'yaxis' if axis_id == 'y1' else f'yaxis{axis_id[1:]}'
            fig.update_layout(**{layout_key: dict(
                title=cfg['title'], side=cfg['side'], position=cfg['position'],
                overlaying=cfg.get('overlaying'), type=cfg.get('type', 'log'),
                showgrid=False, visible=show_axes,
                anchor='free',
            )})

    return fig, stored_df_json

# --- Main ---
if __name__ == '__main__':
    app.run_server(debug=True, port=8052)